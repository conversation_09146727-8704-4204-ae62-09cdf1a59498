const DIET_MODE_MAP = {
    低GI饮食: 'low-gi',
    低脂饮食: 'low-fat',
    低碳水化合物膳食饮食: 'low-purine',
    DASH饮食: 'dash',
    江南饮食: 'jiangnan',
    地中海饮食: 'mediterranean',
    高蛋白饮食: 'high-protein',
}

export const useDiaryStore = defineStore('diary', () => {
    const diaryName = ref('地中海饮食')
    const diaryId = ref('mediterranean')
    const isUseWeightPlanMode = ref(true)

    async function getLatestHealthProgramData() {
        try {
            const { results, state } = await useWrapFetch<BaseResponse<any[]>>('/healthProgram/listHealthProgramPlan')
            if (state === 200 && results?.length) {
                const dietMode = results[0]?.dietPlan?.dietMode
                if (dietMode.startsWith('低碳水化合物')) {
                    diaryName.value = '低碳水化合物饮食'
                    diaryId.value = DIET_MODE_MAP['低碳水化合物膳食饮食']
                } else {
                    diaryName.value = dietMode.replace('模式', '')
                    diaryId.value = DIET_MODE_MAP[diaryName.value as keyof typeof DIET_MODE_MAP]
                }
            } else {
                console.warn('无健康计划数据，保留默认 diaryId 和 diaryName')
            }
        } catch (error) {
            console.error('获取健康计划数据失败:', error)
        }
    }

    nextTick(() => {
        if (isUseWeightPlanMode.value) {
            getLatestHealthProgramData()
        }
    })

    return {
        diaryName,
        diaryId,
        isUseWeightPlanMode,
    }
}, {
    persist: {
        storage: piniaPluginPersistedstate.localStorage(),
    },
})
