export const useFoodScanStore = defineStore('foodScanStore', () => {
    const foodList = ref<FoodItem[]>([])
    const mealEvaluation = ref('')
    const isFoodVisible = ref(false)
    const isScanVisible = ref(false)
    const isFoodSheetShow = ref(false)

    const imgBlobUrl = ref('')
    const imgUrl = ref('')

    const keepFoodAlive = ref(false)

    function resetFoodScan() {
        foodList.value = []
        mealEvaluation.value = ''
        isFoodVisible.value = false
        isScanVisible.value = false
        isFoodSheetShow.value = false
        imgBlobUrl.value = ''
        imgUrl.value = ''
    }

    return {
        foodList,
        mealEvaluation,
        isScanVisible,
        imgBlobUrl,
        imgUrl,
        isFoodVisible,
        keepFoodAlive,
        isFoodSheetShow,
        resetFoodScan,
    }
})
