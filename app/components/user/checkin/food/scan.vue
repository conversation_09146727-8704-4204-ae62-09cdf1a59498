<script setup lang="ts">
import { v4 as uuidv4 } from 'uuid'
import dayjs from 'dayjs'

// const { imgBlobUrl, imgUrl } = defineProps<{
//     imgBlobUrl: string
//     imgUrl: string
// }>()

const emit = defineEmits<{
    (e: 'upload'): void
    (e: 'add', food: FoodItem[], dietType: DietType, mealPicture: string): void
}>()

const appConfig = useAppConfig()
const isDev = appConfig.__IS_DEV__

const mealType = ref(getMealKindByTime())

// const foodList = ref<FoodItem[]>([])
// const mealEvaluation = ref('')

const { foodList, mealEvaluation, isScanVisible, isFoodVisible, keepFoodAlive, isFoodSheetShow, imgBlobUrl, imgUrl } = storeToRefs(useFoodScanStore())

const { resetFoodScan } = useFoodScanStore()

whenever(() => isFoodVisible.value && !keepFoodAlive.value, () => {
    isScanVisible.value = true
    mealEvaluation.value = ''
    foodList.value = []
})

const foodSummary = computed(() => {
    const summary = calculateFoodSummary(foodList.value)

    const details = [
        {
            name: '碳水化合物',
            backgroundStyle: 'background: linear-gradient(90deg, #00AC97 0%, #52D2C2 100%);',
            value: summary.totalCarbohydrates,
        },
        {
            name: '蛋白质',
            backgroundStyle: 'background: linear-gradient(90deg, #3DB5FF 0%, #7ECEFF 100%);',
            value: summary.totalProtein,
        },
        {
            name: '脂肪',
            backgroundStyle: 'background: linear-gradient(90deg, #FFB348 0%, #FFD191 100%);',
            value: summary.totalFat,
        },
        {
            name: '膳食纤维',
            backgroundStyle: 'background: linear-gradient(90deg, #30DD4A 0%, #80F692 100%);',
            value: summary.totalDietaryFiber,
        },
    ]

    return {
        totalCalories: summary.totalCalories,
        details,
    }
})

whenever(imgUrl, async (val) => {
    const filePath = isDev ? `https://test.slmc.top${val}` : `${window.location.origin}${val}`

    const { results } = await useWrapFetch<BaseResponse<{
        food: FoodItem[]
        mealEvaluation: string
    }>>('/checkInCustomerMeal/uploadImage', {
        method: 'post',
        params: {
            filePath,
        },
    })

    mealEvaluation.value = results.mealEvaluation

    foodList.value = (results.food || []).map(item => ({
        ...item,
        uuid: uuidv4().split('-')[0]!,
        mealPicture: '',
        weight: extractNumber(item.weight) || 0,
        calories: extractNumber(item.calories) || 0,
        carbohydrates: extractNumber(item.carbohydrates) || 0,
        protein: extractNumber(item.protein) || 0,
        fat: extractNumber(item.fat) || 0,
        dietaryFiber: extractNumber(item.dietaryFiber) || 0,
        source: 'photo',
    }))
    isScanVisible.value = false
    isFoodSheetShow.value = true
})

function handleActionSheetClose() {
    if (!keepFoodAlive.value) {
        resetFoodScan()
    }
}

function handleRePhoto() {
    keepFoodAlive.value = false
    resetFoodScan()
    emit('upload')
}

function handleAdd() {
    keepFoodAlive.value = false
    emit('add', foodList.value, mealType.value, imgUrl.value)

    handleActionSheetClose()
}

function handleManualCheckin() {
    resetFoodScan()
    isFoodVisible.value = false
    isFoodSheetShow.value = false

    navigateTo(`/user/checkin/food/lib?date=${dayjs().format('YYYY-MM-DD')}`)
}
</script>

<template>
    <div v-show="isFoodVisible" class="fixed top-0 z-101! left-0 w-full h-full bg-white">
        <img :src="imgBlobUrl" class="w-full h-full object-cover" />

        <van-action-sheet
            :show="isFoodSheetShow"
            :overlay="false"
            :lock-scroll="false"
            class="custom-action-sheet"
            @close="handleActionSheetClose"
        >
            <div class="max-h-[calc(100vh-160px)]">
                <div class="flex justify-between items-center h-48px px-16px">
                    <div class="text-center font-600 text-16px text-t-5">餐食识别</div>
                    <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="isFoodSheetShow = false"></div>
                </div>

                <div v-if="foodList.length" class="flex flex-col gap-16px px-16px max-h-[calc(100vh-208px)] overflow-y-auto">
                    <div class="flex items-center gap-12px">
                        <img src="@/assets/images/checkin/calorie.svg" class="w-54px h-54px" />
                        <div>
                            <div class="text-t-5 text-24px font-600">
                                {{ foodSummary.totalCalories }}
                            </div>

                            <div class="text-13px text-t-3">
                                卡路里
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-4 gap-16px">
                        <div v-for="detail in foodSummary.details" :key="detail.name" border="1px solid fill-3" class="rd-10px w-81px h-49px flex flex-col items-center justify-center">
                            <div class="text-t-5 text-16px font-600">
                                {{ detail.value }}g
                            </div>

                            <div class="text-t-3 text-12px">
                                {{ detail.name }}
                            </div>
                        </div>
                    </div>

                    <div>
                        <div class="flex items-center gap-4px">
                            <div class="i-custom:checkin-food-review w-20px h-20px"></div>
                            <div class="text-t-5 font-600 text-14px">
                                餐食评价
                            </div>
                        </div>
                        <div class="text-13px text-t-3 mt-3px">
                            {{ mealEvaluation || '暂无餐食评价' }}
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <div class="meal-btn" :class="{ 'meal-btn-active': mealType === 'breakfast' }" @click="mealType = 'breakfast'">
                            早餐
                        </div>

                        <div class="meal-btn" :class="{ 'meal-btn-active': mealType === 'lunch' }" @click="mealType = 'lunch'">
                            午餐
                        </div>

                        <div class="meal-btn" :class="{ 'meal-btn-active': mealType === 'dinner' }" @click="mealType = 'dinner'">
                            晚餐
                        </div>

                        <div class="meal-btn" :class="{ 'meal-btn-active': mealType === 'snack' }" @click="mealType = 'snack'">
                            加餐
                        </div>
                    </div>

                    <div class="flex flex-col gap-8px">
                        <div class="flex justify-between">
                            <div class="flex items-center gap-4px">
                                <div class="i-custom:checkin-camera-4 w-20px h-20px"></div>
                                <div class="text-t-5 font-600 text-14px">
                                    已识别到的食物
                                </div>
                            </div>
                        </div>

                        <user-checkin-food-editable-list
                            v-model="foodList"
                            from="scan"
                            @change="() => {}"
                        />

                        <div class="w-full flex justify-center items-center mb-160px">
                            <van-button
                                class="w-109px !h-33px"
                                type="primary"
                                plain
                                round
                                @click="() => {
                                    keepFoodAlive = true
                                    navigateTo('/user/checkin/food/lib')
                                }"
                            >
                                + 添加食物
                            </van-button>
                        </div>
                    </div>
                </div>

                <van-empty v-else class="mb-72px text-center" description="未识别到餐食，请重新拍照或手动打卡" />

                <base-fixed-bottom>
                    <div class="flex justify-between px-16px gap-16px w-full">
                        <van-button class="flex-1" plain type="primary" round @click="handleRePhoto">
                            重拍
                        </van-button>

                        <van-button v-if="foodList.length" class="flex-1" type="primary" round @click="handleAdd">
                            确定
                        </van-button>

                        <van-button v-else class="flex-1" plain type="primary" round @click="handleManualCheckin">
                            手动打卡
                        </van-button>
                    </div>
                </base-fixed-bottom>
            </div>
        </van-action-sheet>

        <shared-full-scan v-if="isScanVisible" v-model:visible="isScanVisible" />
    </div>
</template>

<style scoped>
:deep(.custom-action-sheet) {
    background: linear-gradient(180deg, #EBFFFE 0%, #FFFFFF 44%, #FFFFFF 100%);
}

.meal-btn {
    --uno: bg-fill-1 text-t-4 flex items-center justify-center rd-100px w-74px h-30px;
}

.meal-btn-active {
    --uno: bg-primary-6 text-white font-600;
}
</style>
