<script setup lang="ts">
import type { UploaderBeforeRead } from 'vant'

const props = defineProps({
    capture: {
        type: String,
    },
})

const emit = defineEmits<{
    (e: 'add', food: FoodItem[], dietType: DietType, mealPicture: string): void
}>()

const uploaderRef = useTemplateRef<any>('uploaderRef')

const { isFoodVisible, imgBlobUrl, imgUrl } = storeToRefs(useFoodScanStore())

function manualTriggerUpload() {
    uploaderRef.value?.chooseFile()
}

defineExpose({
    manualTriggerUpload,
})

const isUploading = ref(false)

provide('isUploading', isUploading)

async function uploadFile(file: File) {
    try {
        isUploading.value = true
        const formData = new FormData()
        formData.append('file', file)
        const { results } = await useWrapFetch<BaseResponse<string>>('/v1/file/upload', {
            body: formData,
            method: 'post',
        })

        imgUrl.value = formatPreviewResource(results)
    } catch (error) {
        console.log(error)
    } finally {
        isUploading.value = false
    }
}

async function handleImageRead(file: File) {
    try {
        imgUrl.value = ''
        imgBlobUrl.value = ''
        isFoodVisible.value = true
        const fileMbSize = file.size / 1024 / 1024

        if (fileMbSize > 100) {
            showFailToast('图片大小不能超过100MB')
            return false
        }

        const blobUrl = URL.createObjectURL(file)
        imgBlobUrl.value = blobUrl

        if (fileMbSize > 5) {
            const compressedFile = await compressImage(file)
            uploadFile(compressedFile)
        } else {
            uploadFile(file)
        }
    } catch (error) {
        showFailToast('上传图片失败')
    }
}
</script>

<template>
    <van-uploader
        ref="uploaderRef"
        :before-read="(handleImageRead as UploaderBeforeRead)"
        :capture="capture"
    >
        <slot></slot>
    </van-uploader>

    <user-checkin-food-scan
        @upload="manualTriggerUpload"
        @add="(food, dietType, mealPicture) => emit('add', food, dietType, mealPicture)"
    />
</template>
