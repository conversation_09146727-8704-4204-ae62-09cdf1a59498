<script setup lang="ts">
import { cloneDeep } from 'lodash-es'

const { dietType: currentDietType = undefined, from = '' } = defineProps<{
    dietType?: DietType
    from?: string
}>()

const emit = defineEmits<{
    (e: 'change', dietType: DietType, refresh?: boolean): void
}>()

const checkinFoodRoots = inject<Ref<CheckinFoodRoot[]>>('checkinFoodRoots', ref([]))

const foodList = defineModel<FoodItem[]>('modelValue')!

const showFoodEdit = ref(false)
const choosedFood = ref<FoodItem>({
    uuid: '',
    name: '',
    weight: 0,
    calories: 0,
    foodType: '',
    carbohydrates: 0,
    protein: 0,
    fat: 0,
    dietaryFiber: 0,
    mealPicture: '',
    source: 'lib',
})

function handleEditWeight(food: FoodItem) {
    choosedFood.value = cloneDeep(food)
    showFoodEdit.value = true
}

function handleEditWeightSave(dietType: DietType) {
    if (currentDietType && dietType !== currentDietType) {
        // 删除当前饮食，并且添加新的饮食到对应的type
        const currentCheckinFoodRoot = checkinFoodRoots.value.find(item => item.kind === currentDietType)
        currentCheckinFoodRoot!.mealContent.items = (currentCheckinFoodRoot!.mealContent.items as FoodItem[]).filter(item => item.uuid !== choosedFood.value!.uuid)
        // 添加新的饮食到对应的type
        const newCheckinFoodRoot = checkinFoodRoots.value.find(item => item.kind === dietType)
        newCheckinFoodRoot?.mealContent.items.push(choosedFood.value!)
        emit('change', currentDietType, false)
        emit('change', dietType, true)
        showFoodEdit.value = false
    } else {
        const index = foodList.value!.findIndex(item => item.uuid === choosedFood.value!.uuid)
        if (index !== -1) {
            foodList.value![index] = choosedFood.value!
            emit('change', dietType)
            showFoodEdit.value = false
        }
    }
}

function handleDelete(food: FoodItem) {
    showConfirmDialog({
        title: '确定要删除该餐食吗？',
        message: '删除后无法恢复，请谨慎操作',
    }).then(() => {
        foodList.value = foodList.value!.filter(item => item.uuid !== food.uuid)
        emit('change', currentDietType!, true)
        showSuccessToast('删除成功')
        showFoodEdit.value = false
    })
}

// 左滑删除引导相关
const firstSwipeCellRef = ref()
const hasShownSwipeGuide = ref(false)

function getSwipeGuideKey() {
    return 'swipe_guide_shown'
}

function initSwipeGuide() {
    hasShownSwipeGuide.value = localStorage.getItem(getSwipeGuideKey()) === 'true'
}

function performSwipeGuide() {
    if (hasShownSwipeGuide.value || !foodList.value?.length) return

    setTimeout(() => {
        firstSwipeCellRef.value?.open('right')

        setTimeout(() => {
            firstSwipeCellRef.value?.close()
            localStorage.setItem(getSwipeGuideKey(), 'true')
            hasShownSwipeGuide.value = true
        }, 1500)
    }, 500)
}

function triggerSwipeGuide() {
    if (!hasShownSwipeGuide.value && foodList.value?.length) {
        nextTick(performSwipeGuide)
    }
}

function handleEditSingleData() {
    const foodEditUUID = localStorage.getItem('foodEditUUID')
    const targetFood = foodList.value?.find(item => item.uuid === foodEditUUID)
    if (foodEditUUID && targetFood) {
        choosedFood.value = targetFood
        localStorage.removeItem('foodEditUUID')
        setTimeout(() => {
            showFoodEdit.value = true
        }, 30)
    }
}

watch(() => foodList.value, (newVal) => {
    triggerSwipeGuide()
    if (newVal?.length) {
        handleEditSingleData()
    }
},
{ deep: true, immediate: true },
)

onMounted(() => {
    initSwipeGuide()
    triggerSwipeGuide()
})
</script>

<template>
    <div class="flex flex-col gap-10px">
        <van-swipe-cell
            v-for="(food, index) in foodList"
            :key="food.uuid"
            :ref="el => { if (index === 0) firstSwipeCellRef = el }"
        >
            <div class="flex gap-4px justify-between items-center active:bg-gray-1">
                <div class="flex items-start gap-8px">
                    <div class="flex items-center gap-8px">
                        <van-image :src="parseFoodImage(food)" class="w-50px h-50px flex-shrink-0 rd-10px overflow-hidden" :alt="food.foodType">
                            <template #error>
                                <img src="/images/foodTypes/others.png" alt="" srcset="" />
                            </template>
                        </van-image>

                        <div class="flex flex-col">
                            <div class="text-13px font-600 text-t-5 line-clamp-1">
                                {{ food.name }}
                            </div>

                            <div class="text-11px text-t-3">
                                {{ food.weight }}g
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center gap-8px flex-shrink-0" @click="handleEditWeight(food)">
                    <div class="text-12px text-t-4 leading-none">
                        {{ food.calories }}千卡
                    </div>
                    <div class="i-custom:pen w-12px h-12px mb-2px"></div>
                </div>
            </div>

            <template #right>
                <van-button square text="删除" type="danger" class="delete-button" @click="handleDelete(food)" />
            </template>
        </van-swipe-cell>

        <user-checkin-food-part-edit
            v-model:show="showFoodEdit"
            v-model="choosedFood"
            mode="edit"
            :from="from"
            @save="handleEditWeightSave"
            @delete="handleDelete"
        />
    </div>
</template>
