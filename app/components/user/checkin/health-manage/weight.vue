<script setup lang="ts">
import dayjs from 'dayjs'

import { canShowEncourageToday, computeWeightWeekStatus, getBmiText, markEncourageShownToday } from '@/utils/common'
import TimeRangeChart from './time-range-chart.vue'
import { CHECKIN_TOOLS } from '@/utils/checkinCard'

const props = defineProps<{
    waistCircumferenceNum: number
    isVisible?: boolean
}>()

const { latestHealthProgramPlan } = storeToRefs(useBadgeStore())
const timeRangeChartRef = ref<InstanceType<typeof TimeRangeChart>>()
const showInitialWeightSheet = ref(false)
const showWeightTargetSheet = ref(false)
const showWeightSheet = ref(false)
const diaryNum = ref<number>(0)
const showWeightLossTarget = ref(false)
const weightLossTarget = ref(3)
const relevantRecordWeight = ref<number>(0)
const isInitialized = ref(false)
const showEncouragePopup = ref(false)
const weightWeekStatus = ref<boolean[]>([])
const { userInfo } = storeToRefs(useUserStore())

const { data: initialWeight, refresh: refreshInitialWeight } = useAPI<string>('/user/getInitialWeight')
const { data: archiveResults, refresh: refreshArchive } = useAPI<{ archiveWeight: string, archiveHeight: string, archiveBmi: string }>('/user/preliminaryArchive')
const { data: customerIndexResults, refresh: refreshCustomerIndex } = useAPI<{ monthlyWeightIndex: string, weightIndex: string }>('/checkInCustomerIndex/get', { method: 'post', body: {} })
weightLossTarget.value = Number((customerIndexResults.value?.results as any)?.monthlyWeightIndex) || 3

const initialWeightNum = computed(() => Number(initialWeight.value?.results) || 0)
const weightAndHeight = computed(() => {
    const data = (archiveResults.value?.results || {}) as { archiveWeight: string, archiveHeight: string }
    return {
        weight: data.archiveWeight || '',
        height: data.archiveHeight || '',
    }
})
const targetWeightNum = computed(() => {
    const data = (customerIndexResults.value?.results || {}) as { weightIndex: string }
    return Number(data.weightIndex) || 0
})

const diaryCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'diary'))
const waistCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'waistCircumference'))
const weightCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'weight'))

const bmiStatus = computed(() => {
    if (targetWeightNum.value > Number(archiveResults.value?.results?.archiveWeight)) {
        return '正常体重'
    }

    // 优先使用档案BMI，备选从健康计划问卷获取
    const questionnaire = latestHealthProgramPlan.value?.weightManagementQuestionnaire
    const bmi = archiveResults.value?.results?.archiveBmi
        || (questionnaire
            ? (() => {
                    try {
                        return JSON.parse(questionnaire)?.bmi
                    } catch {
                        return null
                    }
                })()
            : null)

    return getBmiText(bmi)
})

const bmiTip = computed(() => {
    const textMap = {
        低体重: '不建议减重',
        正常体重: '建议维持',
        超重: '本月目标',
        肥胖: '本月目标',
        重度肥胖: '本月目标',
    }
    return textMap[bmiStatus.value as keyof typeof textMap]
})

const computedWeight = computed(() => {
    if (relevantRecordWeight.value) {
        if (!['不建议减重', '建议维持'].includes(bmiTip.value)) {
            return (Number(relevantRecordWeight.value) || Number(weightAndHeight.value?.weight))
        }
    }
    return 0
})

const endVal = computed(() => computedWeight.value - weightLossTarget.value)

const bmiRangeText = computed(() => {
    const questionnaire = latestHealthProgramPlan.value?.weightManagementQuestionnaire
    if (!questionnaire) return '18.5-23.9'

    try {
        const parsedData = JSON.parse(questionnaire)
        const age = parsedData?.age || 18
        const bmiRanges = [
            { max: 65, text: '18.5-23.9' }, // 18-65岁
            { max: 80, text: '20-26.9' }, // 65-80岁
            { max: Infinity, text: '22-26.9' }, // 80岁以上
        ]
        return bmiRanges.find(range => age < range.max)?.text || '18.5-23.9'
    } catch {
        return '18.5-23.9'
    }
})

interface ChartData {
    labels: string[]
    data: number[]
}

interface WeightChartData {
    周: ChartData
    月: ChartData
    年: ChartData
}

const weightChartData = reactive<WeightChartData>({
    周: {
        labels: [],
        data: [],
    },
    月: {
        labels: [],
        data: [],
    },
    年: {
        labels: [],
        data: [],
    },
})

function findRelevantWeightRecord(results: any[]) {
    if (!Array.isArray(results) || results.length === 0) return null

    // 获取当月1号的日期
    const currentMonthFirstDay = dayjs().startOf('month').format('YYYY-MM-DD')

    // 1. 尝试找这个月1号的记录
    const firstDayRecord = results.find(item => item.checkInDate === currentMonthFirstDay)
    if (firstDayRecord) return firstDayRecord?.weight

    // 2. 找离这个月1号以后最早的记录
    const laterRecords = results.filter(item => item.checkInDate > currentMonthFirstDay)
    if (laterRecords.length > 0) {
        // 按日期排序，找最早的
        return laterRecords.sort((a, b) =>
            dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
        )[0]?.weight
    }

    // 3. 找离这个月1号以前最晚的记录
    const earlierRecords = results.filter(item => item.checkInDate < currentMonthFirstDay)
    if (earlierRecords.length > 0) {
        // 按日期排序，找最晚的
        return earlierRecords.sort((a, b) =>
            dayjs(b.checkInDate).diff(dayjs(a.checkInDate)),
        )[0]?.weight
    }

    return null // 没有符合条件的记录
}

async function getCustomRangeWeightData() {
    try {
        const startDate = dayjs().startOf('month').subtract(1, 'month').format('YYYY-MM-DD')
        const endDate = dayjs().format('YYYY-MM-DD')

        const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerWeight/getWeightTrendData', {
            method: 'post',
            body: {
                startDate,
                endDate,
            },
        })

        // 计算本周（周日-周六，包含今天）的打卡状态
        weightWeekStatus.value = computeWeightWeekStatus(results)

        relevantRecordWeight.value = findRelevantWeightRecord(results)
    } catch (error) {
        console.error('获取自定义范围体重数据失败:', error)
        return null
    }
}

// 存储一年的原始数据
let yearlyWeightData: any[] = []

async function loadYearlyWeightData() {
    const startDate = dayjs().subtract(365, 'day').format('YYYY-MM-DD')
    const endDate = dayjs().format('YYYY-MM-DD')

    const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerWeight/getWeightTrendData', {
        method: 'post',
        body: {
            startDate,
            endDate,
        },
    })

    if (Array.isArray(results)) {
        yearlyWeightData = results.sort((a, b) => dayjs(a.checkInDate).diff(dayjs(b.checkInDate)))
        // 初始化所有tab的数据
        updateChartDataForTab('周')
        updateChartDataForTab('月')
        updateChartDataForTab('年')
    }
}

function filterDataByTab(tab: '周' | '月' | '年') {
    const now = dayjs()
    let startDate: dayjs.Dayjs

    switch (tab) {
        case '周':
            startDate = now.subtract(7, 'day')
            break
        case '月':
            startDate = now.subtract(30, 'day')
            break
        case '年':
            startDate = now.subtract(365, 'day')
            break
    }

    return yearlyWeightData.filter(item =>
        dayjs(item.checkInDate).isAfter(startDate) || dayjs(item.checkInDate).isSame(startDate, 'day'),
    )
}

function updateChartDataForTab(tab: '周' | '月' | '年') {
    const filteredData = filterDataByTab(tab)

    const labels = filteredData.map((item) => {
        const date = dayjs(item.checkInDate)
        return date.format('M月D日')
    })
    const data = filteredData.map(item => Number(item.weight))

    weightChartData[tab] = {
        labels,
        data,
    }
}

function handleTabChange(tab: '周' | '月' | '年') {
    updateChartDataForTab(tab)
}

async function getDiaryCount() {
    try {
        const { results } = await useWrapFetch<BaseResponse<number>>('/diary/count')
        diaryNum.value = results || 0
    } catch (error) {
        console.error('获取日记数量失败:', error)
    }
}

function handleShowWeightLossTarget() {
    const data = (customerIndexResults.value?.results || {}) as { weightIndex: string }
    const weightIndex = Number(data?.weightIndex)
    if (!weightIndex) {
        showToast('请先设置体重目标')
        return
    }

    if (!['不建议减重', '建议维持'].includes(bmiTip.value)) {
        showWeightLossTarget.value = true
    }
}

async function handleWeightLossTargetConfirm(data: { weightLossTarget: number, targetWeight: number }) {
    try {
        await useWrapFetch('/checkInCustomerIndex/save', { method: 'post', body: {
            monthlyWeightIndex: data.weightLossTarget,
            weightIndex: data.targetWeight,
        } })
        weightLossTarget.value = data.weightLossTarget
    } catch {
        showFailToast('保存失败')
    }
    showWeightLossTarget.value = false
    refreshCustomerIndex()
}

function handleWeightLossTargetCancel() {
    showWeightLossTarget.value = false
}

async function initData() {
    if (isInitialized.value) return

    await Promise.allSettled([
        getDiaryCount(),
        loadYearlyWeightData(),
        getCustomRangeWeightData(),
    ])
    isInitialized.value = true
}

// 只有当组件可见时才初始化
watch(
    () => props.isVisible,
    (visible) => {
        if (visible && !isInitialized.value) {
            initData()
        }
    },
    { immediate: true },
)
</script>

<template>
    <div class="flex flex-col gap-10px">
        <div class="flex h-144px gap-8px">
            <div class="flex flex-col flex-1 justify-around bg-white rd-10px p-12px">
                <div class="flex gap-4px items-center justify-center">
                    <div class="flex flex-col flex-1" @click="showInitialWeightSheet = true">
                        <div class="flex items-center justify-center">
                            <span class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="initialWeightNum"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="1"
                                />
                            </span>
                            <span class="text-#868F9C text-12px font-400 relative top-3px">kg</span>
                        </div>
                        <div class="text-#868F9C text-12px font-400 text-center mt-3px">初始体重</div>
                    </div>
                    <img src="@/assets/images/checkin/two-arrow.svg" alt="" srcset="" class="w-15px h-14px" />
                    <div class="flex flex-col flex-1" @click="handleShowWeightLossTarget">
                        <div
                            v-if="!['不建议减重', '建议维持'].includes(bmiTip)"
                            class="flex items-center justify-center"
                        >
                            <span class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="endVal"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="1"
                                />
                            </span>
                            <span class="text-#868F9C text-12px font-400 relative top-3px">kg</span>
                        </div>
                        <div
                            v-else
                            class="text-#00AC97 text-16px font-600 flex justify-center items-center"
                        >
                            {{ bmiStatus }}
                        </div>
                        <div class="text-#868F9C text-12px font-400 text-center mt-3px">
                            {{ bmiTip }}
                        </div>
                    </div>
                </div>
                <van-button
                    type="primary" color="#4E5969" round class="flex items-center justify-center gap-8px w-full !h-34px"
                    @click="showWeightSheet = true"
                >
                    <div class="flex items-center justify-center w-full h-full gap-8px">
                        <div class="i-custom-plus w-16px h-16px inline-block"></div>
                        <span>记录体重</span>
                    </div>
                </van-button>
            </div>
            <div
                class="w-138px flex flex-col justify-between gap-10px bg-white p-12px rd-10px"
                @click="navigateTo(weightCard?.path)"
            >
                <div class="w-full text-center text-#868F9C text-12px font-400">当前体重</div>
                <div class="flex justify-center">
                    <div class="i-custom:checkin-weight-4 w-32px h-32px"></div>
                </div>
                <div class="flex justify-center items-end gap-4px">
                    <div class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                        <v-countup
                            :options="{ useGrouping: false }"
                            :end-val="weightAndHeight.weight"
                            :start-val="0"
                            :duration="1"
                            :decimal-places="1"
                        />
                    </div>
                    <div class="text-#868F9C text-12px font-400">kg</div>
                </div>
            </div>
        </div>
        <time-range-chart
            ref="timeRangeChartRef"
            title="目标进度"
            subtitle="体重(kg)"
            :chart-data="weightChartData"
            :is-visible="isVisible"
            @tab-change="handleTabChange"
        >
            <template #empty-state>
                <div class="i-custom:checkin-weight-4 w-32px h-32px"></div>
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="showWeightSheet = true"
                >
                    去记录
                </div>
            </template>
        </time-range-chart>
        <div class="flex gap-10px rd-10px">
            <user-checkin-tools-items-card
                v-if="diaryCard"
                class="flex-1"
                :meta="{
                    ...diaryCard,
                    value: diaryNum,
                    title: '轻轻日记',
                }"
                @click="navigateTo(diaryCard.path)"
            />
            <user-checkin-tools-items-card
                v-if="waistCard"
                class="flex-1"
                :meta="{
                    ...waistCard,
                    value: waistCircumferenceNum,
                    showQuick: false,
                }"
                @click="navigateTo(waistCard.path)"
            />
        </div>

        <user-checkin-initial-weight
            v-model="showInitialWeightSheet"
            :initial-weight="initialWeightNum"
            @success="() => {
                refreshInitialWeight()
                showInitialWeightSheet = false
            }"
        />

        <user-checkin-weight-target
            v-model="showWeightTargetSheet"
            @success="() => {
                refreshCustomerIndex()
                showWeightTargetSheet = false
            }"
        />

        <user-checkin-weight
            v-model="showWeightSheet"
            :weight-and-height="weightAndHeight"
            @success="async () => {
                refreshArchive()
                showWeightSheet = false
                loadYearlyWeightData()
                await getCustomRangeWeightData()
                if (canShowEncourageToday('weight', userInfo?.phone)) {
                    showEncouragePopup = true
                    markEncourageShownToday('weight', userInfo?.phone)
                }
            }"
        />

        <user-checkin-health-manage-weight-loss-target
            v-model="showWeightLossTarget"
            :computed-weight="computedWeight"
            :weight-and-height="weightAndHeight"
            :bmi-range-text="bmiRangeText"
            :initial-weight-loss-target="weightLossTarget"
            :initial-target-weight="targetWeightNum"
            @confirm="handleWeightLossTargetConfirm"
            @cancel="handleWeightLossTargetCancel"
        />

        <user-checkin-health-manage-encourage-popup
            v-model:show="showEncouragePopup"
            type="weight"
            :week-status="weightWeekStatus"
        />
    </div>
</template>
