<script setup lang="ts">
import Chart from 'chart.js/auto'

interface ChartDataType {
    labels: string[]
    data: number[]
}

interface ChartDataMap {
    周: ChartDataType
    月: ChartDataType
    年: ChartDataType
}

interface Props {
    title: string
    subtitle?: string
    chartData: ChartDataMap
    isVisible?: boolean
}

type TabType = keyof ChartDataMap

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'tabChange', tab: TabType): void
}>()

const TABS: TabType[] = ['周', '月', '年']
const POINT_WIDTH = 50
const CANVAS_HEIGHT = 120
const CHART_COLORS = {
    primary: '#00AC97',
    grid: '#E5E7EB',
    gradientStart: 'rgba(0, 172, 151, 0.1)',
    gradientEnd: 'rgba(0, 172, 151, 0.1)',
    transparent: 'rgba(0,0,0,0)',
} as const

const chartCanvasRef = ref<HTMLCanvasElement | null>(null)
const activeTab = ref(0)

let chartInstance: Chart | null = null

const currentTab = computed(() => {
    const tab = TABS[activeTab.value]
    return tab ?? '周'
})

defineExpose({
    currentTab,
})

function getCurrentData(): ChartDataType {
    return props.chartData[currentTab.value]
}

function isDataValid(data: ChartDataType): boolean {
    return Boolean(data?.labels?.length)
}

function createGradientBackground(chart: Chart) {
    const { chartArea, ctx } = chart
    if (!chartArea) return CHART_COLORS.transparent

    const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom)
    gradient.addColorStop(0, CHART_COLORS.gradientStart)
    gradient.addColorStop(0.8571, CHART_COLORS.gradientEnd)
    return gradient
}

function renderChart(): void {
    try {
        const currentData = getCurrentData()

        if (!isDataValid(currentData)) {
            return
        }

        if (props.isVisible === false) {
            return
        }

        if (chartInstance) {
            chartInstance.destroy()
        }

        const canvas = chartCanvasRef.value!
        if (!canvas) {
            return
        }

        const container = canvas.parentElement!
        if (!container) {
            return
        }

        if (container.clientWidth === 0) {
            nextTick(() => {
                if (props.isVisible !== false) {
                    renderChart()
                }
            })
            return
        }

        const canvasWidth = Math.max(currentData.labels.length * POINT_WIDTH, container.clientWidth)
        canvas.width = canvasWidth
        canvas.height = CANVAS_HEIGHT

        chartInstance = new Chart(canvas.getContext('2d')!, {
            type: 'line',
            data: {
                labels: currentData.labels,
                datasets: [{
                    data: currentData.data,
                    borderColor: CHART_COLORS.primary,
                    pointBackgroundColor: CHART_COLORS.primary,
                    pointBorderColor: CHART_COLORS.primary,
                    pointRadius: 2,
                    borderWidth: 2,
                    tension: 0.4,
                    fill: 'start',
                    backgroundColor: context => createGradientBackground(context.chart),
                }],
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false },
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: { color: CHART_COLORS.grid },
                    },
                    x: {
                        grid: { color: CHART_COLORS.grid },
                        ticks: { autoSkip: false, minRotation: 0, maxRotation: 0 },
                    },
                },
            },
        })
    } catch (error) {
        console.error('Error rendering chart:', error)
    }
}

function handleTabClick(index: number): void {
    activeTab.value = index
    emit('tabChange', TABS[index]!)
}

onMounted(() => {
    renderChart()
})

onUnmounted(() => {
    chartInstance?.destroy()
})

watch(
    () => props.chartData,
    () => {
        const currentData = getCurrentData()
        if (isDataValid(currentData)) {
            renderChart()
        }
    },
    { deep: true },
)

watch(
    () => props.isVisible,
    (newVisible) => {
        if (newVisible) {
            nextTick(() => {
                renderChart()
            })
        }
    },
)

const isEmpty = computed(() => !props.chartData[currentTab.value]?.data?.length)
</script>

<template>
    <div class="w-full flex flex-col bg-white rd-10px p-12px" :class="$attrs.class">
        <div class="flex justify-between items-center w-full">
            <div class="flex items-center gap-8px">
                <h3 class="text-#1D2229 text-14px font-600 m-0">{{ title }}</h3>
                <p v-if="subtitle" class="text-#868F9C text-12px font-400 m-0">{{ subtitle }}</p>
            </div>

            <div class="flex w-140px h-25px bg-#F2F4F7 rd-8px p-2px">
                <button
                    v-for="(tab, index) in TABS"
                    :key="tab"
                    class="h-full flex flex-1 items-center justify-center rd-6px text-12px cursor-pointer transition-all duration-300 border-none bg-transparent text-#4E5969 font-400"
                    :class="{ 'tab-button--active': activeTab === index }"
                    @click="handleTabClick(index)"
                >
                    {{ tab }}
                </button>
            </div>
        </div>

        <div class="chart-container w-full h-fit relative overflow-y-hidden scrollbar-hide">
            <canvas ref="chartCanvasRef" class="h-120px block no-swipe"></canvas>
            <div
                v-if="isEmpty && activeTab === 0"
                class="absolute inset-0 flex flex-col items-center justify-center gap-8px empty-chart-bg"
            >
                <slot name="empty-state"></slot>
            </div>
        </div>
    </div>
</template>

<style scoped>
.tab-button--active {
    @apply bg-white text-#1D2229 font-600;
}

.empty-chart-bg {
    background: url('@/assets/images/service/empty-chart.png') center center no-repeat, #FFFFFF96;
    background-size: 100% 100%;
}

.chart-container {
    scrollbar-width: thin;
    scrollbar-color: #b4bcc8 #f2f4f7;
}

.chart-container::-webkit-scrollbar {
    height: 8px;
    background: #f2f4f7;
    border-radius: 8px;
}

.chart-container::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, #e5e7eb 10%, #b4bcc8 90%);
    border-radius: 8px;
    border: 2px solid #f2f4f7;
    min-width: 30px;
    transition: background 0.3s;
}

.chart-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, #00AC97 10%, #b4bcc8 90%);
}

.chart-container::-webkit-scrollbar-corner {
    background: #f2f4f7;
}
</style>
