export function calculateFoodSummary(foodList: FoodItem[]) {
    const totalCalories = foodList.reduce((acc, food) => acc + extractNumber(food.calories), 0).toFixed(0)
    const totalCarbohydrates = foodList.reduce((acc, food) => acc + extractNumber(food.carbohydrates), 0).toFixed(0)
    const totalProtein = foodList.reduce((acc, food) => acc + extractNumber(food.protein), 0).toFixed(0)
    const totalFat = foodList.reduce((acc, food) => acc + extractNumber(food.fat), 0).toFixed(0)
    const totalDietaryFiber = foodList.reduce((acc, food) => acc + extractNumber(food.dietaryFiber), 0).toFixed(0)
    return {
        totalCalories,
        totalCarbohydrates,
        totalProtein,
        totalFat,
        totalDietaryFiber,
    }
}

export function generateCheckinFoodRoots(kind: DietType, checkInDate: string): CheckinFoodRoot {
    return {
        id: null,
        kind,
        mealContent: {
            kind: null,
            pictures: [],
            items: [],
        },
        checkInDate,
        status: 0,
        calorie: 0,
    }
}

export async function saveDietRecord(foodList: FoodItem[], dietType: DietType, mealPicture?: string, date?: string) {
    try {
        const { default: dayjs } = await import('dayjs')
        let checkinFoodRoots: CheckinFoodRoot[] = []
        const checkInDate = date || dayjs().format('YYYY-MM-DD')
        const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerMeal/list', {
            method: 'POST',
            body: {
                checkInDate,
            },
        })

        checkinFoodRoots = results?.map((item) => {
            const mealContent = JSON.parse(item.mealContent)

            // 兼容旧数据
            if (Array.isArray(mealContent)) {
                return {
                    ...item,
                    mealContent: {
                        kind: 'recommend',
                        items: mealContent[0],
                    },
                }
            }

            return {
                ...item,
                mealContent,
            }
        }) || []

        const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'] as DietType[]
        mealTypes.forEach((kind) => {
            if (!checkinFoodRoots.find(item => item.kind === kind)) {
                checkinFoodRoots.push(generateCheckinFoodRoots(kind, checkInDate))
            }
        })

        const findIndex = checkinFoodRoots.findIndex(item => item.kind === dietType)
        if (findIndex !== -1) {
            const findFoodRoot = checkinFoodRoots[findIndex]!
            findFoodRoot.mealContent.kind = 'picture'
            findFoodRoot.mealContent.items = [
                ...(findFoodRoot.mealContent.items as FoodItem[]),
                ...foodList,
            ]

            if (mealPicture) {
                findFoodRoot.mealContent.pictures = [
                    ...(findFoodRoot.mealContent.pictures as string[]),
                    mealPicture,
                ]
            }
            const totalCalorie = (findFoodRoot.mealContent.items as FoodItem[]).reduce((acc, item) => acc + extractNumber(item.calories), 0)

            await useWrapFetch<BaseResponse<number>>('/checkInCustomerMeal/save', {
                method: 'POST',
                body: {
                    ...findFoodRoot,
                    status: 1,
                    calorie: totalCalorie,
                    mealContent: JSON.stringify(findFoodRoot.mealContent),
                },
            })
        }
    } catch (error) {
        console.error(error)
        throw new Error(error as any)
    }
}

export function parseFoodPart(original: string) {
    const result = {
        carbohydrates: 0,
        protein: 0,
        fat: 0,
        dietaryFiber: 0,
    }
    try {
        const parsed = JSON.parse(original)
        parsed.forEach((item: any) => {
            if (item.name === '碳水化合物') {
                result.carbohydrates = extractNumber(item.value)
            } else if (item.name === '蛋白质') {
                result.protein = extractNumber(item.value)
            } else if (item.name === '脂肪') {
                result.fat = extractNumber(item.value)
            } else if (item.name === '膳食纤维') {
                result.dietaryFiber = extractNumber(item.value)
            }
        })
    } catch (error) {
        console.error(error)
    }

    return result
}

export function parseFoodImage(food: FoodItem) {
    if (food.mealPicture) {
        return food.mealPicture
    }

    return `/images/foodTypes/${food.foodType}.png`
}
