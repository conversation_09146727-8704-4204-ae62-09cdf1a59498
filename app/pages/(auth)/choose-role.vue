<script setup lang="ts">
useHead({
    title: '选择角色',
})

definePageMeta({
    meta: {
        layout: {
            customBg: 'image-background-gradient',
        },
    },
})

const phone = useRoute().query.phone as string

const config = useRuntimeConfig()

async function loginByRole(role: 'user' | 'manager') {
    const { results } = await useWrapFetch<BaseResponse<any>>('/open-api/v1/wx/phoneLoginAccordingToRole', {
        params: {
            role,
            phone,
        },
    })

    if (results.status === 'init') {
        sessionStorage.setItem('token', results.token)
        sessionStorage.setItem('headImage', results.headImgUrl)
        sessionStorage.setItem('nickName', results.nickname)
        sessionStorage.setItem('teamId', String(results.teamId) || '')

        if (role === 'user')
            navigateTo('/user-register')

        if (role === 'manager')
            navigateTo(`/manager-register?phone=${phone}`)
    } else if (results.status === 'login') {
        const { userInfo, token: userToken, role: _role } = storeToRefs(useUserStore())

        userInfo.value = {
            headImage: results.headImgUrl,
            nickName: results.nickname,
            phone: results.phone,
            name: results.name,
            idCard: results.idCard,
            duties: results.duties,
            teamId: results.teamId,
            operationType: results.operationType,
        }
        userToken.value = results.token
        _role.value = role

        if (role === 'user') {
            const wx = await useWxBridge({})

            const dataToBase64 = encodeMessage({
                type: 'user:login',
                data: 'login',
                userStore: userInfo.value,
                token: results.token,
            })

            wx?.miniProgram.redirectTo({
                url: `/pages/index/index?message=${dataToBase64}`,
            })
            // navigateTo('/user', { replace: true })
        }

        if (role === 'manager') {
            const wx = await useWxBridge({})

            const dataToBase64 = encodeMessage({
                type: 'manager:login',
                data: 'login',
                userStore: userInfo.value,
                token: results.token,
            })

            wx?.miniProgram.redirectTo({
                url: `/pages/index/index?message=${dataToBase64}`,
            })
            // navigateTo('/manager', { replace: true })
        }
    }
}
</script>

<template>
    <div p-40px flex="~ col">
        <div text-primary-6 mt-100px>
            Hi，很高兴见到您
        </div>
        <div text="18px primary-6" font-500>
            请选择身份进行登录
        </div>

        <div class="choose-card" mt-40px flex items-center gap-10px @click="loginByRole('user')">
            <div class="i-custom-user-enter w-36px h-30px"></div>
            <div>
                <div text="16px t-5" font-500>
                    客户入口
                </div>

                <div text-t-3>
                    填写定制问卷，关注自身健康
                </div>
            </div>
        </div>

        <div
            class="choose-card" mt-16px flex items-center gap-10px @click="() => {
                if (config.public.branch === 'main') {
                    showToast('敬请期待')
                    return
                }
                else {
                    loginByRole('manager')
                }
            }"
        >
            <div class="i-custom-manager-enter w-36px h-30px"></div>
            <div>
                <div text="16px t-5" font-500>
                    医护/运营入口
                </div>

                <!-- TODO: 相同描述？ -->
                <div text-t-3>
                    填写定制问卷，关注自身健康
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.choose-card {
    --uno: bg-white rd-4px p-12px;
    box-shadow: 0px 4px 20px 0px rgba(3, 28, 67, 0.06);
}
</style>
