<script setup lang="ts">
import { v4 as uuidv4 } from 'uuid'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '食物库',
})

const dayjs = useDayjs()
const active = ref(0)

const route = useRoute()
const checkInDate = route.query.date as string || dayjs().format('YYYY-MM-DD')
const parentNodes = ref<any[]>([])
const subNodes = ref<any[]>([])
const { keepFoodAlive, foodList } = storeToRefs(useFoodScanStore())
const router = useRouter()

function formatSubNodes(subResults: any[]) {
    return subResults
        .map((item) => {
            const foodType = item.foodClassification

            function addFoodType(arr: any[]) {
                return Array.isArray(arr) ? arr.map(food => ({ ...food, foodType })) : []
            }

            return [
                ...addFoodType(item.aiFoods),
                ...addFoodType(item.aiIngredients),
                ...addFoodType(item.aiPackagedFoods),
            ]
        })
        .flat()
        .filter(Boolean)
}

const showSidebar = ref(true)

const localFoods = ref<any[]>([])

const { status, refresh } = useAsyncData('init', async () => {
    try {
        showSidebar.value = true
        const raw = await fetch('/no-cache/food-lib.json')
        const { foods } = await raw.json()
        localFoods.value = foods
        parentNodes.value = foods.map((item: any) => item.type)

        subNodes.value = localFoods.value[0].children
    }
    catch (error) {
        console.log(error)
    }
})

const showFoodEdit = ref(false)

const childNodesLoading = ref(false)

const scrollerRef = useTemplateRef<any>('scrollerRef')

function handleChooseParent(value: any) {
    const localChildren = localFoods.value.find((item: any) => item.type === value)?.children || []
    subNodes.value = localChildren
    nextTick(() => {
        scrollerRef.value?.scrollToPosition(0)
    })
}

const choosedFood = ref<FoodItem>({
    uuid: '',
    name: '',
    weight: 0,
    calories: 0,
    foodType: '',
    carbohydrates: 0,
    protein: 0,
    fat: 0,
    dietaryFiber: 0,
    mealPicture: '',
    source: 'lib',
})

function handleChooseFood(item: any) {
    const { carbohydrates, protein, fat, dietaryFiber } = parseFoodPart(item.nutrientsPer100g)

    choosedFood.value = {
        uuid: uuidv4().split('-')[0]!,
        name: item.foodName as string,
        weight: 100,
        foodType: '',
        carbohydrates,
        calories: extractNumber(item.caloriesPer100g),
        protein,
        fat,
        dietaryFiber,
        mealPicture: item.foodImage || '',
        source: 'lib',
    }

    // 拍照识别餐食编辑来到这里，选择完食物之后直接回到识别餐食页面
    if (route.query.from === 'scan') {
        const targetIndex = foodList.value.findIndex(food => food.uuid === route.query.uuid)
        if (targetIndex !== -1) {
            const originalUuid = foodList.value[targetIndex]!.uuid
            const updatedFood = {
                ...choosedFood.value,
                uuid: originalUuid, // 保留原来的uuid
            }
            foodList.value[targetIndex] = updatedFood
            keepFoodAlive.value = true
            localStorage.setItem('foodEditUUID', originalUuid)
            router.back()
        }
        return
    }

    showFoodEdit.value = true
}

const searchValue = ref('')

async function handleSave(dietType: DietType, saveType: 'add' | 'continue') {
    try {
        if (keepFoodAlive.value) {
            foodList.value.push(choosedFood.value)
            showToast('保存成功')
            if (saveType === 'add') {
                router.back()
            } else {
                showFoodEdit.value = false
            }
        } else {
            await saveDietRecord([choosedFood.value!], dietType, '', checkInDate)
            showToast('保存成功')
            if (saveType === 'add') {
                navigateTo(`/user/checkin/food?date=${checkInDate}&dietCheck=1`, { replace: true })
            } else {
                showFoodEdit.value = false
            }
        }
    } catch (error) {
        console.log(error)
    }
}

let storeKeyword = ''
async function searchFood(foodName: string) {
    if (!foodName) {
        refresh()
        return
    }

    try {
        storeKeyword = foodName
        showSidebar.value = false
        childNodesLoading.value = true
        const { results } = await useWrapFetch<BaseResponse<any>>('/checkInFoodRecommend/listFoodClassificationsByFoodName', {
            params: {
                foodName,
            },
        })

        subNodes.value = formatSubNodes([results])
    } catch (error) {
        showSidebar.value = true
    } finally {
        childNodesLoading.value = false
    }
}

function highlightText(text: string) {
    if (!storeKeyword) return text

    const reg = new RegExp(storeKeyword, 'gi')
    return text.replace(reg, match => `<span class="text-primary-6">${match}</span>`)
}

function handleClearSearch() {
    searchValue.value = ''
    storeKeyword = ''
    refresh()
}

const debouncedSearch = useDebounceFn((value: string) => {
    searchFood(value)
}, 300)

watch(searchValue, (newValue) => {
    if (newValue.length >= 1) {
        debouncedSearch(newValue)
    } else if (newValue.length === 0) {
        handleClearSearch()
    }
})
</script>

<template>
    <base-suspense :status="status">
        <van-search
            v-model="searchValue" shape="round" clearable clear-trigger="always"
            placeholder="请输入搜索关键词" @clear="handleClearSearch" @search="searchFood"
        >
            <template #action>
                <div class="flex items-center gap-4px">
                    <div class="text-primary-6" @click="searchFood(searchValue)">
                        搜索
                    </div>
                </div>
            </template>
        </van-search>
        <div class="flex">
            <van-sidebar v-if="showSidebar" v-model="active" :class="childNodesLoading ? 'pointer-events-none' : ''">
                <van-sidebar-item
                    v-for="(item, index) in parentNodes || []" :key="index" :title="item"
                    @click="handleChooseParent(item)"
                />
            </van-sidebar>

            <div v-if="childNodesLoading" class="flex-1 px-10px overflow-hidden">
                <van-skeleton>
                    <template #template>
                        <div class="flex flex-1 mt-10px">
                            <div :style="{ flex: 1 }">
                                <van-skeleton-paragraph v-for="i in 12" :key="i" class="h-50px! rd-10px" />
                            </div>
                        </div>
                    </template>
                </van-skeleton>
            </div>

            <div v-else class="flex-1">
                <template v-if="subNodes.length > 0">
                    <recycle-scroller
                        ref="scrollerRef" :items="subNodes" :item-size="60" key-field="foodName"
                        class="scroller-height"
                    >
                        <template #default="{ item }">
                            <div
                                class="food-item flex items-center px-16px py-12px active:bg-gray-100"
                                @click="handleChooseFood(item)"
                            >
                                <van-image
                                    :src="item.foodImage || `/images/foodTypes/${item.foodType}.png`"
                                    class="w-50px h-50px rd-4px mr-8px overflow-hidden"
                                >
                                    <template #error>
                                        <img src="/images/foodTypes/others.png" alt="" srcset="" />
                                    </template>
                                </van-image>
                                <div class="flex-1">
                                    <div
                                        class="text-14px text-t-5 font-600 line-clamp-1"
                                        v-html="highlightText(item.foodName)"
                                    ></div>
                                    <div class="text-12px text-t-4 mt-4px">
                                        <span class="text-primary-6">{{
                                            extractNumber(item.caloriesPer100g)
                                        }}</span>千卡/100克
                                    </div>
                                </div>
                                <div class="i-custom-add-2 w-14px h-14px text-primary-6"></div>
                            </div>
                        </template>
                    </recycle-scroller>
                </template>
                <van-empty v-else description="暂无食物数据" class="mt-100px" />
            </div>
        </div>

        <user-checkin-food-part-edit v-model:show="showFoodEdit" v-model="choosedFood" @save="handleSave" />
    </base-suspense>
</template>

<style scoped>
.van-sidebar {
    height: calc(100vh - 56px);
    width: 120px;
}

.van-sidebar-item--select {
    color: rgb(var(--primary-6));
}

.scroller-height {
    height: calc(100vh - 56px);
}

.food-item {
    border-bottom: 1px solid var(--van-gray-3);
}

.food-item:last-child {
    border-bottom: none;
}

:deep(.text-primary-6) {
    color: rgb(var(--primary-6));
}
</style>
