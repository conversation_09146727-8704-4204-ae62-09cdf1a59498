<script setup lang="ts">
import type WaistTrend from '~/components/user/checkin/waist-trend.vue'

const dayjs = useDayjs()

const TABS = ['周', '月', '年'] as const
type TabType = (typeof TABS)[number]

interface ChartData {
    labels: string[]
    data: number[]
}
interface WaistChartData {
    周: ChartData
    月: ChartData
    年: ChartData
}

const waistTrendChartRef = ref<InstanceType<typeof WaistTrend>>()
const waistChartData = reactive<WaistChartData>({
    周: { labels: [], data: [] },
    月: { labels: [], data: [] },
    年: { labels: [], data: [] },
})
const someWaistCircumferenceData = ref<any[]>([])
const showWaistSheet = ref(false)

const { data: waistData, refresh } = useAPI<{
    waistCircumference: number
    waistCircumferenceTrend: number
}>('/checkInCustomerWaistCircumference/get', {
    method: 'post',
    body: { kind: '6', checkInDate: '' },
})
const { data: archiveResults, refresh: refreshArchive } = useAPI<Archives>('/user/preliminaryArchive')

async function getWaistCircumferenceTrendByTypeWithTrend() {
    const res = await useWrapFetch<BaseResponse<any>>('/api/checkInCustomerWaistCircumference/getWaistCircumferenceTrendByTypeWithTrend', {
        method: 'get',
        params: { type: 'all' },
    })
    someWaistCircumferenceData.value = res.results.waistCircumferenceList.slice(0, 5)
}

// 存储一年的原始数据
let yearlyWaistData: Array<{ checkInDate: string, waistCircumference: number }> = []

async function loadYearlyWaistData() {
    try {
        const params = { type: 'year', value: dayjs().format('YYYY') }
        const res = await useWrapFetch<{
            results: {
                waistCircumferenceList: {
                    checkInDate: string
                    waistCircumference: number
                }[]
            }
        }>('/api/checkInCustomerWaistCircumference/getWaistCircumferenceTrendByTypeWithTrend', {
            method: 'GET',
            params,
        })
        if (res?.results?.waistCircumferenceList) {
            yearlyWaistData = res.results.waistCircumferenceList.sort((a, b) => dayjs(a.checkInDate).valueOf() - dayjs(b.checkInDate).valueOf())
            // 初始化所有tab的数据
            updateChartDataForTab('周')
            updateChartDataForTab('月')
            updateChartDataForTab('年')
        }
    } catch (error) {
        console.error('获取腰围趋势数据失败:', error)
    }
}

function filterWaistDataByTab(tab: TabType) {
    const now = dayjs()
    let startDate: dayjs.Dayjs

    switch (tab) {
        case '周':
            startDate = now.subtract(7, 'day')
            break
        case '月':
            startDate = now.subtract(30, 'day')
            break
        case '年':
            startDate = now.subtract(365, 'day')
            break
    }

    return yearlyWaistData.filter(item =>
        dayjs(item.checkInDate).isAfter(startDate) || dayjs(item.checkInDate).isSame(startDate, 'day')
    )
}

function updateChartDataForTab(tab: TabType) {
    const filteredData = filterWaistDataByTab(tab)
    waistChartData[tab] = {
        labels: filteredData.map(item => dayjs(item.checkInDate).format('MM月DD日')),
        data: filteredData.map(item => item.waistCircumference),
    }
}

function handleTabChange(tab: TabType) {
    updateChartDataForTab(tab)
}

onMounted(() => {
    getWaistCircumferenceTrendByTypeWithTrend()
    loadYearlyWaistData()
})

function handleWeightSuccess() {
    showWaistSheet.value = false
    refresh()
    getWaistCircumferenceTrendByTypeWithTrend()
    loadYearlyWaistData()
}
</script>

<template>
    <div class="p-16px h-full flex flex-col gap-12px">
        <user-checkin-waist-trend
            ref="waistTrendChartRef"
            :chart-data="waistChartData"
            :is-visible="true"
            @tab-change="handleTabChange"
        >
            <template #title>
                <div class="flex items-center gap-8px">
                    <div class="text-t-5 text-14px font-500">
                        腰围趋势
                    </div>
                    <div class="text-11px flex items-center gap-2px text-t-3">
                        变化值
                        <div
                            :class="(waistData?.results?.waistCircumferenceTrend || 0) > 0 ? 'text-#F98804' : (waistData?.results?.waistCircumferenceTrend || 0) < 0 ? 'text-#00AC97' : 'text-t-3'"
                        >
                            {{ (waistData?.results?.waistCircumferenceTrend || 0) > 0 ? '↑' : (waistData?.results?.waistCircumferenceTrend || 0) < 0 ? '↓' : '-' }}
                            {{ (waistData?.results?.waistCircumferenceTrend || 0) !== 0 ? Math.abs((waistData?.results?.waistCircumferenceTrend || 0)).toFixed(2) : '' }}
                        </div>
                    </div>
                </div>
            </template>
            <template #empty-state>
                <div class="i-custom:checkin-waist w-32px h-32px"></div>
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="showWaistSheet = true"
                >
                    去记录
                </div>
            </template>
        </user-checkin-waist-trend>

        <div v-if="someWaistCircumferenceData.length" class="bg-white rd-10px p-16px flex flex-col gap-12px w-full">
            <div class="flex items-end gap-8px text-t-5 text-14px font-600">
                腰围记录
            </div>
            <div class="flex flex-col gap-6px">
                <div v-for="item in someWaistCircumferenceData" :key="item.checkInDate" class="flex items-center justify-between gap-8px">
                    <div class="text-t-4 text-13px">{{ dayjs(item.checkInDate).format('MM月DD日') }}</div>
                    <div class="flex items-center gap-8px text-t-5 text-18px font-600">
                        {{ item.waistCircumference }}cm
                    </div>
                </div>
            </div>
            <div
                v-if="someWaistCircumferenceData.length > 5"
                class="flex items-center justify-center text-t-3 text-13px cursor-pointer"
                @click="navigateTo('/user/checkin/waist-records')"
            >
                查看更多 >
            </div>
        </div>
        <van-empty v-else description="暂无腰围数据" />
    </div>
    <div class="flex fixed bottom-36px left-0 right-0 justify-center">
        <van-button round type="primary" class="w-223px! h-50px!" @click="showWaistSheet = true">记录今日腰围</van-button>
    </div>
    <user-checkin-waist
        v-model="showWaistSheet" :waist="Number(archiveResults?.results?.waist)"
        @success="handleWeightSuccess"
    />
</template>

<style lang="scss">
.box {
    &:before {
        background-color: #00B42A;
    }
}
</style>
