<script setup lang="ts">
import { showImagePreview } from 'vant'

import WeightProgressChart from '@/components/user/checkin/diary/WeightProgressChart.vue'
import VoiceRecorder from '@/components/user/checkin/diary/VoiceRecorder.vue'

import type { UploaderBeforeRead, UploaderFileListItem } from 'vant/es/uploader/types'
import type WXSDK from 'weixin-js-sdk'

useHead({
    title: '日记详情',
})

// 常量
const MAX_PICTURES = 3

// 类型定义
interface DiaryPicture extends UploaderFileListItem {
    type: 'upload' | 'meal'
}

// 工具实例
const dayjs = useDayjs()
const route = useRoute()
const router = useRouter()
const { parseDiaryContent } = useDiary()

// 微信SDK实例
let wx: typeof WXSDK | null = null

// 响应式状态
const detail = ref<DiaryDetail>()
const weightData = reactive({
    historyWeight: 0,
    currentWeight: 0,
    targetWeight: 0,
})
const archiveHeight = ref(0)
const showWeightSheet = ref(false)
const isRecording = ref(false)
const isSaving = ref(false)
const imgUrl = ref('')
const diaryContentData = ref<DiaryContent>({
    content: '',
})

// 图片相关状态
const uploadPictures = ref<DiaryPicture[]>([])
const mealPictures = ref<DiaryPicture[]>([])
const selectedMealPictures = ref<Set<string>>(new Set())
const displayPictures = ref<DiaryPicture[]>([])
const picturePickerShow = ref(false)

// 计算属性
const totalPictureCount = computed(() => {
    return uploadPictures.value.length + selectedMealPictures.value.size
})

const diaryContent = computed({
    get: () => diaryContentData.value.content,
    set: (value) => {
        if (!diaryContentData.value) {
            diaryContentData.value = { content: '' }
        }
        diaryContentData.value.content = value
    },
})

// 初始化微信SDK
async function initWx() {
    try {
        const _wx = await useWxBridge({
            jsApiList: ['startRecord', 'stopRecord', 'onVoiceRecordEnd', 'translateVoice'],
        })
        wx = _wx
    } catch (error) {
        console.error('初始化微信SDK失败:', error)
    }
}

// 工具函数
async function fetchData<T>(
    url: string,
    options: {
        method: 'get' | 'post' | 'put'
        body?: any
    } = { method: 'get' },
) {
    try {
        const response = await useWrapFetch<BaseResponse<T>>(url, options)
        return response.results
    } catch (error) {
        console.error(`获取${url}数据失败:`, error)
        return undefined
    }
}

// 图片处理相关函数
async function handleBeforeRead(file: File | File[]): Promise<File | File[] | undefined> {
    try {
        // 检查是否是合法的文件对象
        if (!file || (Array.isArray(file) && file.length === 0)) {
            showToast('无效的文件')
            return undefined
        }

        // 将单个文件转换为数组以统一处理
        const files = Array.isArray(file) ? file : [file]

        // 检查总数是否超出限制
        if (totalPictureCount.value + files.length > MAX_PICTURES) {
            showToast(`最多只能添加${MAX_PICTURES}张图片`)
            return undefined
        }

        // 处理每个文件
        for (const f of files) {
            // 确保文件对象有效
            if (!f || !(f instanceof File)) {
                showToast('无效的文件')
                continue
            }

            const fileMbSize = f.size / 1024 / 1024

            if (fileMbSize > 100) {
                showToast('图片大小不能超过100MB')
                return undefined
            }

            try {
                let processedFile = f
                if (fileMbSize > 5) {
                    processedFile = await compressImage(f)
                }

                const url = await uploadFile(processedFile)
                if (url) {
                    const newPicture: DiaryPicture = { url, type: 'upload' }
                    uploadPictures.value.push(newPicture)
                }
            } catch (error) {
                showToast(`处理文件 ${f.name} 失败`)
                return undefined
            }
        }

        return file
    } catch (error) {
        showToast('上传图片失败')
        return undefined
    }
}

async function uploadFile(file: File) {
    const { closeLoading } = useLoading({
        message: '上传中...',
    })
    try {
        // 创建新的 FormData 实例
        const formData = new FormData()
        formData.append('file', file)

        const { results } = await useWrapFetch<BaseResponse<string>>('/v1/file/upload', {
            body: formData,
            method: 'post',
            headers: {
                Accept: 'application/json',
            },
        })

        imgUrl.value = formatResource(results)
        return imgUrl.value
    } catch (error) {
        showToast('上传失败')
        return ''
    } finally {
        closeLoading()
    }
}

function handlePreview(file: UploaderFileListItem) {
    showImagePreview([file.url!])
}

function handleDelete(file: UploaderFileListItem) {
    const url = file.url
    if (url) {
        selectedMealPictures.value.delete(url)
    }
    uploadPictures.value = uploadPictures.value.filter(item => item.url !== file.url)
    displayPictures.value = displayPictures.value.filter(item => item.url !== file.url)
}

function togglePictureSelection(picture: UploaderFileListItem) {
    const url = picture.url
    if (!url) return

    if (selectedMealPictures.value.has(url)) {
        selectedMealPictures.value.delete(url)
    } else {
        if (totalPictureCount.value >= MAX_PICTURES) {
            showToast(`最多只能添加${MAX_PICTURES}张图片`)
            return false
        }
        selectedMealPictures.value.add(url)
    }
}

function handlePicturePickerShow(show: boolean) {
    if (show) {
        uploadPictures.value = displayPictures.value.filter(pic => pic.type === 'upload')
        selectedMealPictures.value = new Set(
            displayPictures.value
                .filter(pic => pic.type === 'meal')
                .map(pic => pic.url)
                .filter((url): url is string => url !== undefined),
        )
    }
    picturePickerShow.value = show
}

function handleConfirm() {
    const selectedPictures = mealPictures.value
        .filter(pic => pic.url && selectedMealPictures.value.has(pic.url))
        .map(pic => ({ ...pic, type: 'meal' as const }))
    displayPictures.value = [...uploadPictures.value, ...selectedPictures]
    picturePickerShow.value = false
}

function handleCancel() {
    picturePickerShow.value = false
}

// 业务函数
async function getDiaryDetail() {
    const id = route.params.id
    if (id === '0') {
        detail.value = {} as DiaryDetail
        diaryContentData.value = { content: '' }
    }

    try {
        const [detailData, archiveResults, customerIndexResults, initialWeight] = await Promise.allSettled([
            id === '0' ? Promise.resolve(undefined) : fetchData<DiaryDetail>(`/diary/${id}`),
            fetchData<Archives>('/user/preliminaryArchive'),
            fetchData<CustomerIndex>('/checkInCustomerIndex/get', { method: 'post', body: {} }),
            fetchData<number>('/user/getInitialWeight'),
        ])

        if (id !== '0' && detailData.status === 'fulfilled' && detailData.value) {
            detail.value = detailData.value
            if (detail.value.content) {
                diaryContentData.value = parseDiaryContent(detail.value.content)
                isRecording.value = !!diaryContentData.value.timestamp
            }
            if (detail.value.pictures) {
                try {
                    const pictures = JSON.parse(detail.value.pictures) as DiaryPicture[]
                    uploadPictures.value = pictures.filter(pic => pic.type === 'upload')
                    const mealPics = pictures.filter(pic => pic.type === 'meal')
                    mealPics.forEach((pic) => {
                        if (pic.url) {
                            selectedMealPictures.value.add(pic.url)
                        }
                    })
                    displayPictures.value = pictures
                } catch (error) {
                    console.error('解析图片数据失败:', error)
                    uploadPictures.value = []
                    displayPictures.value = []
                }
            }
        } else if (id !== '0') {
            showToast('获取日记详情失败')
            detail.value = {} as DiaryDetail
            diaryContentData.value = { content: '' }
        }

        if (initialWeight.status === 'fulfilled'
            && customerIndexResults.status === 'fulfilled'
            && archiveResults.status === 'fulfilled'
            && initialWeight.value
            && customerIndexResults.value?.weightIndex
            && archiveResults.value?.archiveWeight) {
            archiveHeight.value = Number(archiveResults.value.archiveHeight) || 0
            weightData.historyWeight = initialWeight.value
            weightData.currentWeight = Number(archiveResults.value.archiveWeight) || 0
            weightData.targetWeight = Number(customerIndexResults.value.weightIndex) || 0
        }

        fetchData<string[]>('/api/checkInCustomerMeal')
            .then((data) => {
                if (!data) return

                const pictures = data.reduce<DiaryPicture[]>((acc, item) => {
                    try {
                        const parsed = JSON.parse(item)
                        if (parsed.kind === 'picture' && Array.isArray(parsed.items)) {
                            const validPictures = parsed.items
                                .filter((pic: { mealPicture?: string }) => pic?.mealPicture)
                                .map((pic: { mealPicture: string }) => ({
                                    url: pic.mealPicture,
                                    type: 'meal' as const,
                                }))
                            acc.push(...validPictures)
                        }
                        return acc
                    } catch (e) {
                        console.error('解析餐食数据失败:', e)
                        return acc
                    }
                }, [])

                mealPictures.value = pictures
            })
            .catch((error) => {
                console.error('获取餐食数据失败:', error)
            })
    } catch (error) {
        console.error('获取数据失败:', error)
        showToast('获取数据失败')
    }
}

function toggleRecording() {
    isRecording.value = !isRecording.value
    if (isRecording.value) {
        const timestamp = dayjs().format('MM-DD HH:mm')
        diaryContentData.value.timestamp = timestamp
    } else {
        delete diaryContentData.value.timestamp
    }
}

async function handleSave() {
    if (isSaving.value) return
    isSaving.value = true

    try {
        if (!diaryContentData.value.content) {
            showToast('请输入日记内容')
            isSaving.value = false
            return
        }

        const requestBody = {
            weight: weightData.currentWeight,
            content: JSON.stringify(diaryContentData.value),
            pictures: JSON.stringify(displayPictures.value),
        }

        let response
        if (!detail.value?.id) {
            response = await useWrapFetch<BaseResponse<number>>('/api/diary', {
                method: 'post',
                body: requestBody,
            })
        } else {
            response = await useWrapFetch<BaseResponse<number>>('/api/diary', {
                method: 'put',
                body: {
                    ...requestBody,
                    id: detail.value.id,
                },
            })
        }

        if (response.state === 200) {
            showSuccessToast('保存成功')
            router.back()
        } else {
            showToast(response.msg || '保存失败')
        }
    } catch (error) {
        console.error('保存日记失败:', error)
        showToast('保存失败')
    } finally {
        isSaving.value = false
    }
}

// 生命周期钩子
onMounted(() => {
    if (isIOS()) {
        const url = new URL(location.href)
        if (!url.searchParams.has('reloaded')) {
            url.searchParams.set('reloaded', '1')
            location.replace(`${url.pathname}?${url.searchParams.toString()}`)
        }
    }
    getDiaryDetail()
    initWx()
})
</script>

<template>
    <div class="py-12px px-16px h-100vh">
        <div class="h-100% bg-white br-10px py-16px px-12px flex flex-col justify-between">
            <div>
                <div class="h-132px w-100% relative">
                    <weight-progress-chart
                        :history-weight="weightData.historyWeight"
                        :current-weight="weightData.currentWeight"
                        :target-weight="weightData.targetWeight"
                    />
                    <div v-if="!weightData.currentWeight" class="absolute inset-0 flex flex-col items-center justify-center gap-8px empty-chart-bg">
                        <div class="i-custom:checkin-weight-4 w-32px h-32px"></div>
                        <div
                            class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                            @click="showWeightSheet = true"
                        >
                            记录今日体重
                        </div>
                    </div>
                </div>
                <div class="flex flex-wrap gap-16px mb-16px mt-4px">
                    <div
                        v-for="(picture, index) in displayPictures"
                        :key="picture.url || ''"
                        class="w-80px h-80px bg-white overflow-hidden relative border-1px border-#F2F4F7 rd-10px"
                    >
                        <img
                            v-if="picture.url"
                            :src="picture.url"
                            class="w-100% h-100% rd-10px"
                            @click="showImagePreview({ images: displayPictures.map(pic => pic.url).filter((url): url is string => !!url), startPosition: index })"
                        />
                        <img
                            src="@/assets/icons/checkin/radio-delete.svg"
                            class="absolute top-0px right-0px w-24px h-24px rd-tr-10px cursor-pointer"
                            @click="handleDelete(picture)"
                        />
                    </div>
                    <div
                        class="w-80px h-80px bg-#F2F4F7 text-#4E5969 border-1px border-dashed border-#E5E7EB rd-10px flex flex-col items-center justify-center"
                        @click="handlePicturePickerShow(true)"
                    >
                        <div class="w-24px h-24px bg-[url('@/assets/icons/checkin/checkin-camera.svg')] bg-no-repeat bg-center bg-contain"></div>
                        <div class="text-13px font-400">
                            添加照片
                        </div>
                    </div>
                </div>

                <div class="rd-10px bg-#F2F4F7 border-1px border-#E5E7EB mt-16px grid grid-rows-[1fr,auto] overflow-hidden">
                    <van-field
                        v-model="diaryContent"
                        type="textarea"
                        placeholder="认真记录的你真的很棒，告诉我今天的计划完成的如何吧！你可以用图片、文字记录今天。"
                        rows="3"
                        :autosize="{ maxHeight: 170 }"
                        maxlength="500"
                    />
                    <div v-if="isRecording" class="pl-16px text-#1D2229 flex items-center gap-4px my-8px">
                        <img src="@/assets/icons/checkin/record.svg" />{{ diaryContentData.timestamp }}
                    </div>
                </div>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex gap-16px">
                    <div
                        class="w-42px h-42px rd-100px border-1px flex items-center justify-center cursor-pointer"
                        :class="isRecording ? 'border-#6AD9CB bg-#6AD9CB' : 'border-#E4FAF9 bg-#E4FAF9'"
                        @click="toggleRecording"
                    >
                        <div v-show="!isRecording" class="w-16px h-16px bg-[url('~/assets/icons/checkin/record-active.svg')] bg-no-repeat bg-center bg-contain"></div>
                        <div v-show="isRecording" class="w-16px h-16px bg-[url('~/assets/icons/checkin/record-white.svg')] bg-no-repeat bg-center bg-contain"></div>
                    </div>
                    <voice-recorder
                        :wx="wx"
                        @translate="(result) => {
                            diaryContent = diaryContent ? `${diaryContent}${result}` : result
                        }"
                    />
                </div>
                <div
                    class="w-120px h-42px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer"
                    :class="isSaving || !diaryContent ? 'bg-#CCCCCC border-1px border-#CCCCCC text-white' : 'bg-#00AC97 border-1px border-#00AC97 text-white'"
                    :style="{ pointerEvents: isSaving || !diaryContent ? 'none' : 'auto' }"
                    @click="handleSave"
                >
                    保存日记
                </div>
            </div>
        </div>
    </div>
    <van-action-sheet v-model:show="picturePickerShow" title="选择照片" @cancel="handleCancel" @click-overlay="handleCancel">
        <div class="p-16px flex flex-col gap-16px">
            <div class="flex items-center text-15px text-#1D2229 font-400 title-with-bar">上传照片</div>
            <van-uploader
                :model-value="uploadPictures"
                multiple
                :max-count="MAX_PICTURES"
                :disabled="totalPictureCount >= MAX_PICTURES"
                :before-read="(handleBeforeRead as UploaderBeforeRead)"
                @preview="handlePreview"
                @delete="handleDelete"
            >
                <div
                    class="w-86px h-71px bg-#F2F4F7 text-#4E5969 border-1px border-dashed border-#E5E7EB rd-10px flex flex-col items-center justify-center"
                >
                    <div class="w-24px h-24px bg-[url('@/assets/icons/checkin/checkin-camera.svg')] bg-no-repeat bg-center bg-contain"></div>
                    <div class="text-13px font-400">
                        添加照片
                    </div>
                </div>
            </van-uploader>
            <div class="flex items-center text-15px text-#1D2229 font-400 mb-16px title-with-bar">选择打卡照片</div>
            <div class="flex flex-wrap gap-16px">
                <div
                    v-for="picture in mealPictures"
                    :key="picture.url || ''"
                    class="w-100px h-100px bg-white border-1px border-#F2F4F7 rd-10px flex flex-col items-center justify-center overflow-hidden relative cursor-pointer"
                    @click="togglePictureSelection(picture)"
                >
                    <img v-if="picture.url" :src="picture.url" class="w-100% h-100% rd-10px" />
                    <div
                        v-if="picture.url && selectedMealPictures.has(picture.url)"
                        class="absolute top-0px right-0px w-24px h-24px bg-[#54524b] rd-tr-10px rd-bl-10px bg-[url('~/assets/icons/checkin/radio-checked.png')] bg-[length:16px_16px] bg-no-repeat bg-center"
                    ></div>
                    <div
                        v-else-if="picture.url"
                        class="absolute top-0px right-0px w-24px h-24px bg-[#54524b] rd-tr-10px rd-bl-10px bg-[url('~/assets/icons/checkin/radio-unchecked.png')] bg-[length:16px_16px] bg-no-repeat bg-center"
                    ></div>
                </div>
            </div>
            <div class="flex justify-between">
                <div
                    class="w-160px h-50px flex items-center justify-center rd-100px text-15px font-600 cursor-pointer border-1px text-#00AC97 border-#00AC97"
                    @click="handleCancel"
                >
                    取消
                </div>
                <div
                    class="w-160px h-50px flex items-center justify-center rd-100px text-15px font-600 cursor-pointer bg-#00AC97 text-white border-1px border-#00AC97"
                    @click="handleConfirm"
                >
                    确认
                </div>
            </div>
        </div>
    </van-action-sheet>
    <user-checkin-weight
        v-model="showWeightSheet"
        :weight-and-height="{ weight: String(weightData.currentWeight), height: String(archiveHeight) }"
        @success="(weight) => {
            showWeightSheet = false
            if (weight) {
                weightData.currentWeight = weight
            }
        }"
    />
</template>

<style scoped>
.empty-chart-bg {
    background: url('@/assets/images/service/empty-chart.png') center center no-repeat, #FFFFFF96;
    background-size: 100% 100%;
}

:deep(.van-uploader__preview) {
    width: 100px;
    height: 100px;
    margin: 0;
}

:deep(.van-uploader__preview-image) {
    width: 100%;
    height: 100%;
    border-radius: 10px;
}

:deep(.van-uploader__preview-delete--shadow) {
    width: 24px;
    height: 24px;
    border-top-right-radius: 10px;
    border-bottom-left-radius: 10px;
}

:deep(.van-uploader__preview-delete-icon) {
    top: 50%;
    left: 45%;
    transform: translate(-50%, -50%);
    font-size: 16px;
}

:deep(.van-uploader__input-wrapper) {
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.van-uploader__wrapper) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    width: 100%;
    font-size: 14px;
    font-weight: 400;
}

:deep(.van-cell) {
    background-color: #F2F4F7;
    color: #1D2229;
}

:deep(.van-field__control::placeholder) {
    color: #868F9C;
}

.finger-icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 50%;
    z-index: 1;
}

.title-with-bar {
    position: relative;
    padding-left: 14px;
}

.title-with-bar::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 16px;
    border-radius: 3px;
    background-color: #00AC97;
}

.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity var(--transition-duration) ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
    opacity: 1;
}

.voice-recording-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: #00000099;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.voice-recording-overlay * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.voice-recording-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: center;
}

.voice-recording-gif {
    width: 160px;
    height: 160px;
    background: url('@/assets/icons/checkin/microphone-animated.svg') no-repeat center center;
    background-size: contain;
}

.voice-recording-text {
    color: white;
    font-size: 16px;
    font-weight: 600;
}

.voice-recording-bottom {
    width: 100%;
    height: 139px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.voice-recording-bottom-icon {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.voice-recording-wifi {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
}

.voice-recording-cancel {
    position: absolute;
    left: 44px;
    top: -80px;
    width: 44px;
    height: 44px;
    z-index: 1;
}
</style>
