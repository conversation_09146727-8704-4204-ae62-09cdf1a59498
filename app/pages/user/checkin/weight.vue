<script setup lang="ts">
import dayjs from 'dayjs'

import { canShowEncourageToday, computeWeightWeekStatus, markEncourageShownToday } from '@/utils/common'
import TimeRangeChart from '@/components/user/checkin/health-manage/time-range-chart.vue'

interface ChartData {
    labels: string[]
    data: number[]
}

interface WeightChartData {
    周: ChartData
    月: ChartData
    年: ChartData
}

const timeRangeChartRef = ref<InstanceType<typeof TimeRangeChart>>()

const weightChartData = reactive<WeightChartData>({
    周: {
        labels: [],
        data: [],
    },
    月: {
        labels: [],
        data: [],
    },
    年: {
        labels: [],
        data: [],
    },
})

const { data: initialWeight, refresh: refreshInitialWeight } = useAPI<BaseResponse<string>>('/user/getInitialWeight')
const { data: archiveResults, refresh: refreshArchive } = useAPI<Archives>('/user/preliminaryArchive')

const initialWeightNum = computed(() => Number(initialWeight.value?.results) || 0)

const weightAndHeight = computed(() => {
    return {
        weight: archiveResults.value?.results.archiveWeight || '',
        height: archiveResults.value?.results.archiveHeight || '',
    }
})

const { data: weightData, refresh } = useAPI<{
    weight: number
    weightTrend: number
}>('/checkInCustomerWeight/get', {
    method: 'post',
    body: {
        kind: '6',
        checkInDate: '',
    },
})

const showInitialWeightSheet = ref(false)
const showWeightSheet = ref(false)
const showWeightTargetSheet = ref(false)
const showEncouragePopup = ref(false)
const weightWeekStatus = ref<boolean[]>([])
const { userInfo } = storeToRefs(useUserStore())
const weightTargetRef = useTemplateRef('weightTargetRef')
function handleWeightSuccess() {
    showWeightSheet.value = false
    if (canShowEncourageToday('weight', userInfo.value?.phone)) {
        showEncouragePopup.value = true
        markEncourageShownToday('weight', userInfo.value?.phone)
    }
    refresh()
    refreshArchive()
    getWeightTrendData(timeRangeChartRef.value?.currentTab ?? '周')
}

const progressPercent = ref(0.5)
const progressPath = ref<SVGPathElement | null>(null)

function setupProgressRing() {
    const basePath = document.getElementById('basePath') as unknown as SVGPathElement
    if (!basePath) return

    try {
        const pathLength = basePath.getTotalLength()
        const percent = Number.isFinite(progressPercent.value) ? progressPercent.value : 0

        if (progressPath.value) {
            progressPath.value.setAttribute('stroke-dasharray', `${pathLength}`)
            progressPath.value.setAttribute('stroke-dashoffset', `${pathLength * (1 - percent)}`)
        }
    } catch (error) {
        console.error('Error in setupProgressRing:', error)
    }
}

async function getWeightTrendData(tab: '周' | '月' | '年') {
    let startDate = ''
    const endDate = dayjs().format('YYYY-MM-DD')

    switch (tab) {
        case '周':
            startDate = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
            break
        case '月':
            startDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
            break
        case '年':
            startDate = dayjs().subtract(365, 'day').format('YYYY-MM-DD')
            break
    }

    const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerWeight/getWeightTrendData', {
        method: 'post',
        body: {
            startDate,
            endDate,
        },
    })

    // 计算并填充本周打卡状态
    weightWeekStatus.value = computeWeightWeekStatus(results)

    if (Array.isArray(results)) {
        const sortedData = results.sort((a, b) => dayjs(a.checkInDate).diff(dayjs(b.checkInDate)))

        const labels = sortedData.map((item) => {
            const date = dayjs(item.checkInDate)
            switch (tab) {
                case '周':
                    return date.format('M月D日')
                case '月':
                    return date.format('M月D日')
                case '年':
                    return date.format('M月D日')
                default:
                    return ''
            }
        })
        const data = sortedData.map(item => Number(item.weight))

        weightChartData[tab] = {
            labels,
            data,
        }
    }
}

onMounted(() => {
    setupProgressRing()
    getWeightTrendData('周')
})
</script>

<template>
    <div class="p-16px flex flex-col gap-16px">
        <div class="bg-white rd-10px flex flex-col gap-16px p-16px">
            <div class="flex gap-16px">
                <div class="flex flex-2/5 relative">
                    <svg class="w-full h-full" viewBox="0 0 220 150">
                        <defs>
                            <filter id="dotShadow" x="-20%" y="-20%" width="140%" height="140%">
                                <feDropShadow dx="0" dy="1" stdDeviation="1" flood-color="#eee" />
                            </filter>
                        </defs>
                        <g transform="translate(-10,18)">
                            <path
                                id="basePath"
                                d="M41.72,124.28 A90,90 0 1,1 198.28,124.28"
                                fill="none"
                                stroke="#F5F7FA"
                                stroke-width="12"
                                stroke-linecap="round"
                            />
                            <path
                                ref="progressPath"
                                d="M41.72,124.28 A90,90 0 1,1 198.28,124.28"
                                fill="none"
                                stroke="#00AC97"
                                stroke-width="12"
                                stroke-linecap="round"
                            />
                        </g>
                    </svg>
                    <div class="absolute top-64% left-50% -translate-x-1/2 -translate-y-1/2 w-full h-full flex flex-col items-center justify-center gap-4px">
                        <div class="flex justify-center items-end">
                            <div class="text--#1D2229 text-28px font-800 font-ddinpro leading-none">{{ weightAndHeight.weight }}</div>
                            <div class="text-#868F9C text-12px font-400">kg</div>
                        </div>
                        <div
                            v-if="weightData?.results?.weightTrend"
                            class="px-8px py-2px bg-#E4FAF9 rd-100px h-20px flex items-center justify-center text-10px font-400"
                            :style="{
                                color: (weightData?.results?.weightTrend || 0) > 0 ? '#FBACA3' : '#00AC97',
                            }"
                        >
                            {{ (weightData?.results?.weightTrend || 0) > 0 ? '+' : '-' }}{{ Math.abs((weightData?.results?.weightTrend || 0)).toFixed(2) }}kg
                        </div>
                    </div>
                </div>
                <div class="flex flex-col flex-3/5 gap-16px justify-between bg-white">
                    <div class="flex gap-4px items-center justify-center">
                        <div class="flex flex-col w-60px" @click="showInitialWeightSheet = true">
                            <div class="flex items-center justify-center">
                                <span class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                                    {{ initialWeightNum }}
                                </span>
                                <span class="text-#868F9C text-12px font-400">kg</span>
                            </div>
                            <div class="text-#868F9C text-12px font-400 text-center">初始体重</div>
                        </div>
                        <div class="text-#868F9C text-14px font-400">>></div>
                        <div
                            class="flex flex-col w-60px"
                            @click="showWeightTargetSheet = true"
                        >
                            <div class="flex items-center justify-center">
                                <span class="text-#1D2229 text-20px font-800 font-ddinpro leading-none">
                                    {{ weightTargetRef?.confirmValue }}
                                </span>
                                <span class="text-#868F9C text-12px font-400">kg</span>
                            </div>
                            <div class="text-#868F9C text-12px font-400 text-center">目标体重</div>
                        </div>
                    </div>
                    <van-button
                        type="primary" color="#00AC97" round class="flex items-center justify-center gap-8px w-full !h-34px"
                        @click="showWeightSheet = true"
                    >
                        <div class="flex items-center justify-center w-full h-full gap-8px">
                            <div class="i-custom-plus w-16px h-16px inline-block"></div>
                            <span>记录体重</span>
                        </div>
                    </van-button>
                </div>
            </div>

            <user-bmi-line :root-bmi="archiveResults?.results.archiveBmi" />

            <div class="text-#1D2229 text-13px font-400">
                {{ getBmiTips(Number(archiveResults?.results.archiveBmi)) }}
            </div>
        </div>

        <time-range-chart
            ref="timeRangeChartRef"
            title="体重变化"
            :chart-data="weightChartData"
            :is-visible="true"
            @tab-change="getWeightTrendData"
        >
            <template #empty-state>
                <div class="i-custom:checkin-weight-4 w-32px h-32px"></div>
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="showWeightSheet = true"
                >
                    去记录
                </div>
            </template>
        </time-range-chart>

        <user-checkin-initial-weight
            v-model="showInitialWeightSheet"
            :initial-weight="initialWeightNum"
            @success="() => {
                refreshInitialWeight()
                showInitialWeightSheet = false
            }"
        />

        <user-checkin-weight
            v-model="showWeightSheet"
            :weight-and-height="weightAndHeight"
            @success="handleWeightSuccess"
        />

        <user-checkin-weight-target ref="weightTargetRef" v-model="showWeightTargetSheet" />

        <user-checkin-health-manage-encourage-popup
            v-model:show="showEncouragePopup"
            type="weight"
            :week-status="weightWeekStatus"
        />
    </div>
</template>
