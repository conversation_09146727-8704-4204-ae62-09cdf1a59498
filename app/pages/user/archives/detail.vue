<script setup lang="ts">
import dayjs from 'dayjs'

import TimeRangeChart from '@/components/user/checkin/health-manage/time-range-chart.vue'

useHead({
    title: '个人详情',
})

const { data: archiveResults, refresh: refreshArchive } = useAPI<Archives>('/user/preliminaryArchive')
const timeRangeChartRef = useTemplateRef('timeRangeChartRef')

interface ChartData {
    labels: string[]
    data: number[]
}

interface WeightChartData {
    周: ChartData
    月: ChartData
    年: ChartData
}

const weightChartData = reactive<WeightChartData>({
    周: {
        labels: [],
        data: [],
    },
    月: {
        labels: [],
        data: [],
    },
    年: {
        labels: [],
        data: [],
    },
})

// 存储一年的原始数据
let yearlyWeightData: any[] = []

async function loadYearlyWeightData() {
    const startDate = dayjs().subtract(365, 'day').format('YYYY-MM-DD')
    const endDate = dayjs().format('YYYY-MM-DD')

    const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerWeight/getWeightTrendData', {
        method: 'post',
        body: {
            startDate,
            endDate,
        },
    })

    if (Array.isArray(results)) {
        yearlyWeightData = results.sort((a, b) => dayjs(a.checkInDate).diff(dayjs(b.checkInDate)))
        // 初始化所有tab的数据
        updateChartDataForTab('周')
        updateChartDataForTab('月')
        updateChartDataForTab('年')
    }
}

function filterDataByTab(tab: '周' | '月' | '年') {
    const now = dayjs()
    let startDate: dayjs.Dayjs

    switch (tab) {
        case '周':
            startDate = now.subtract(7, 'day')
            break
        case '月':
            startDate = now.subtract(30, 'day')
            break
        case '年':
            startDate = now.subtract(365, 'day')
            break
    }

    return yearlyWeightData.filter(item =>
        dayjs(item.checkInDate).isAfter(startDate) || dayjs(item.checkInDate).isSame(startDate, 'day')
    )
}

function updateChartDataForTab(tab: '周' | '月' | '年') {
    const filteredData = filterDataByTab(tab)

    const labels = filteredData.map((item) => {
        const date = dayjs(item.checkInDate)
        return date.format('M月D日')
    })
    const data = filteredData.map(item => Number(item.weight))

    weightChartData[tab] = {
        labels,
        data,
    }
}

function handleTabChange(tab: '周' | '月' | '年') {
    updateChartDataForTab(tab)
}

const showWeightSheet = ref(false)

const weightAndHeight = computed(() => {
    const data = (archiveResults.value?.results || {}) as { archiveWeight: string, archiveHeight: string }
    return {
        weight: data.archiveWeight || '',
        height: data.archiveHeight || '',
    }
})

onMounted(() => {
    loadYearlyWeightData()
})
</script>

<template>
    <div class="p-16px">
        <div class="bg-white rd-10px p-16px">
            <div class="font-600 text-t-5">
                个人档案
            </div>

            <div flex justify-between>
                <div bg-fill-1 rd-10px w-93px h-66px flex flex-col px-12px py-8px>
                    <div flex items-center gap-4px>
                        <div bg="#5397E8" h-12px w-4px rd-5px>
                        </div>
                        <div text="14px t-3">
                            体重(kg)
                        </div>
                    </div>

                    <div text="20px t-t" font-600>
                        {{ archiveResults?.results.archiveWeight }}
                    </div>
                </div>

                <div bg-fill-1 rd-10px w-93px h-66px flex flex-col px-12px py-8px>
                    <div flex items-center gap-4px>
                        <div bg="#E88953" h-12px w-4px rd-5px>
                        </div>
                        <div text="14px t-3">
                            BMI指数
                        </div>
                    </div>

                    <div text="20px t-t" font-600>
                        {{ parseFloat(archiveResults?.results.archiveBmi?.toString() || '0').toFixed(1) }}
                    </div>
                </div>

                <div bg-fill-1 rd-10px w-93px h-66px flex flex-col px-12px py-8px>
                    <div flex items-center gap-4px>
                        <div bg="#29DD86" h-12px w-4px rd-5px>
                        </div>
                        <div text="14px t-3">
                            身高(cm)
                        </div>
                    </div>

                    <div text="20px t-t" font-600>
                        {{ archiveResults?.results.archiveHeight }}
                    </div>
                </div>
            </div>

            <user-bmi-line :root-bmi="archiveResults?.results.archiveBmi" />

            <div class="text-t-4 text-13px font-400">
                {{ getBmiTips(Number(archiveResults?.results.archiveBmi)) }}
            </div>

            <time-range-chart
                ref="timeRangeChartRef"
                class="!p-0 mt-10px"
                title="体重趋势"
                subtitle="体重(kg)"
                :chart-data="weightChartData"
                :is-visible="true"
                @tab-change="handleTabChange"
            >
                <template #empty-state>
                    <div class="i-custom:checkin-weight-4 w-32px h-32px"></div>
                    <div
                        class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                        @click="showWeightSheet = true"
                    >
                        去记录
                    </div>
                </template>
            </time-range-chart>
        </div>

        <user-checkin-weight
            v-model="showWeightSheet"
            :weight-and-height="weightAndHeight"
            @success="() => {
                refreshArchive()
                showWeightSheet = false
                loadYearlyWeightData()
            }"
        />
    </div>
</template>
