<script setup lang="ts">
import dayjs from 'dayjs'

interface HealthProgramPlan {
    physicalCondition?: {
        bodyFatRate?: number
        visceralFatArea?: number
    }
}

interface WeightRecord {
    weight: number
    date?: string
}

interface WaistRecord {
    waistCircumference: number
    date?: string
}

interface HealthMetric {
    value: number | null
    trend: 'increase' | 'decrease' | null
    hasData: boolean
}

definePageMeta({
    meta: {
        tabbar: true,
        layout: {
            customBg: 'overflow-auto',
        },
    },
})

const { userInfo, token, role } = storeToRefs(useUserStore())
const { reset } = useUserStore()
const { ageAndGender } = await useAgeAndGender()
const { data } = useAPI<Archives>('/user/preliminaryArchive')
const badgeStore = useBadgeStore()
const debugRef = useTemplateRef('debugRef')
const showBMIInfo = ref(false)

useDebug(debugRef)

const healthProgramPlans = shallowRef<HealthProgramPlan[]>([])
const waistCircumferenceList = shallowRef<WaistRecord[]>([])
const weightList = shallowRef<WeightRecord[]>([])

const config = useRuntimeConfig()

const { data: isOperationStaff } = useAsyncData('isOperationStaff', async () => {
    if (config.public.branch === 'main') {
        return Promise.resolve({
            results: false,
        })
    } else {
        const { results } = await useWrapFetch<BaseResponse<boolean>>('/user/isOperationStaff')
        return {
            results,
        }
    }
})

const navList = computed(() => [
    {
        name: '个人详情',
        icon: 'i-custom-user-cell-detail',
        show: true,
        onClick: () => {
            navigateTo('/user/archives/detail')
        },
    },
    {
        name: '减重记录',
        icon: 'i-custom-user-cell-weight',
        show: true,
        onClick: () => {
            navigateTo('/user/checkin/weight')
        },
    },
    {
        name: '问卷记录',
        icon: 'i-custom-user-cell-survey',
        show: true,
        onClick: () => {
            navigateTo('/user/archives/surveys')
        },
    },
    {
        name: '健康方案',
        icon: 'i-custom-user-cell-health',
        show: true,
        onClick: () => {
            navigateTo('/user/archives/health-report')
        },
        showBadge: badgeStore.badges['/user/archives']?.show || false,
        num: badgeStore.badges['/user/archives']?.num || 0,
    },
    {
        name: '检验检查',
        icon: 'i-custom-user-cell-check',
        show: true,
        onClick: () => {
            navigateTo('/user/archives/inspects')
        },
    },
    {
        name: '订单记录',
        icon: 'i-custom-user-cell-battery',
        show: true,
        onClick: () => {
            showToast('敬请期待')
        },
    },
    // {
    //     name: '穿戴设备',
    //     icon: 'i-custom-user-cell-wear',
    //     onClick: () => {
    //         showToast('敬请期待')
    //     },
    // },
    {
        name: '隐私协议',
        icon: 'i-custom-user-cell-privacy',
        show: true,
        onClick: () => {
            navigateTo('/agreements/privacy')
        },
    },
    {
        name: '切换至运营助手',
        icon: 'i-custom-user-cell-manager',
        show: isOperationStaff.value?.results || false,
        onClick: async () => {
            const phone = userInfo.value!.phone!
            const { results } = await useWrapFetch<BaseResponse<any>>('/open-api/v1/wx/phoneLoginAccordingToRole', {
                params: {
                    role: 'manager',
                    phone,
                },
            })

            reset()

            if (results.status === 'init') {
                sessionStorage.setItem('token', results.token)
                sessionStorage.setItem('headImage', results.headImgUrl)
                sessionStorage.setItem('nickName', results.nickname)
                sessionStorage.setItem('teamId', String(results.teamId) || '')

                navigateTo(`/manager-register?phone=${phone}`)
            } else if (results.status === 'login') {
                userInfo.value = {
                    headImage: results.headImgUrl,
                    nickName: results.nickname,
                    phone: results.phone,
                    name: results.name,
                    idCard: results.idCard,
                    duties: results.duties,
                    teamId: results.teamId,
                    operationType: results.operationType,
                }
                token.value = results.token
                role.value = 'manager'

                const wx = await useWxBridge({})

                const dataToBase64 = encodeMessage({
                    type: 'manager:login',
                    data: 'login',
                    userStore: userInfo.value,
                    token: results.token,
                })

                wx?.miniProgram.redirectTo({
                    url: `/pages/index/index?message=${dataToBase64}`,
                })
            }
        },
    },
])

function calculateTrend(values: (number | null)[]): 'increase' | 'decrease' | null {
    const validValues = values.filter(v => v !== null && v !== undefined) as number[]
    if (validValues.length < 2) return null

    const latest = validValues[0]
    const previous = validValues[1]

    if (latest && previous && latest > previous) return 'increase'
    if (latest && previous && latest < previous) return 'decrease'
    return null
}

function calculateBMI(weight: number, height: number): number {
    if (!weight || !height) return 0
    const heightInMeters = height / 100
    return Number((weight / (heightInMeters * heightInMeters)).toFixed(1))
}

function createHealthMetric<T>(
    dataList: Ref<T[]>,
    getValue: (item: T) => number | null | undefined,
    maxItems = 2,
): ComputedRef<HealthMetric> {
    return computed(() => {
        const items = dataList.value.slice(0, maxItems)
        const values = items.map(getValue).filter(v => v !== null && v !== undefined) as number[]
        const latest = values[0]
        const trend = calculateTrend(values)

        return {
            value: latest || null,
            trend,
            hasData: latest !== null && latest !== undefined,
        }
    })
}

const healthMetricsCache = computed(() => {
    return {
        recentWeights: weightList.value.slice(0, 2),
        recentPlans: healthProgramPlans.value.slice(0, 2),
        recentWaist: waistCircumferenceList.value.slice(0, 2),
    }
})

const weightData = createHealthMetric(weightList, item => item.weight)
const bodyFatData = createHealthMetric(healthProgramPlans, plan => plan.physicalCondition?.bodyFatRate)
const visceralFatData = createHealthMetric(healthProgramPlans, plan => plan.physicalCondition?.visceralFatArea)
const waistCircumferenceData = createHealthMetric(waistCircumferenceList, item => item.waistCircumference)

const bmiData = computed((): HealthMetric => {
    const items = healthMetricsCache.value.recentWeights
    const height = data.value?.results?.archiveHeight

    if (!height || items.length === 0) {
        return { value: null, trend: null, hasData: false }
    }

    const values = items.map(item => calculateBMI(Number(item.weight), Number(height)))
    const latest = values[0]
    const trend = calculateTrend(values)

    return {
        value: latest || null,
        trend,
        hasData: latest !== null && latest !== undefined && latest > 0,
    }
})

async function fetchHealthProgramPlan() {
    try {
        const { results } = await useWrapFetch<BaseResponse<HealthProgramPlan[]>>('/healthProgram/listHealthProgramPlan')
        healthProgramPlans.value = results || []
    } catch (error) {
        console.error('获取健康计划失败:', error)
        healthProgramPlans.value = []
    }
}

async function fetchWaistCircumferenceTrend() {
    try {
        const { results } = await useWrapFetch<BaseResponse<{ waistCircumferenceList: WaistRecord[] }>>('/api/checkInCustomerWaistCircumference/getWaistCircumferenceTrendByTypeWithTrend', {
            method: 'GET',
            params: {
                type: 'all',
            },
        })
        waistCircumferenceList.value = results?.waistCircumferenceList || []
    } catch (error) {
        console.error('获取腰围数据失败:', error)
        waistCircumferenceList.value = []
    }
}

async function fetchWeightTrendData() {
    try {
        const endDate = dayjs().format('YYYY-MM-DD')
        const startDate = dayjs().subtract(1, 'year').format('YYYY-MM-DD')

        const { results } = await useWrapFetch<BaseResponse<WeightRecord[]>>('/api/checkInCustomerWeight/getWeightTrendData', {
            method: 'POST',
            body: {
                startDate,
                endDate,
            },
        })
        weightList.value = results || []
    } catch (error) {
        console.error('获取体重数据失败:', error)
        weightList.value = []
    }
}

async function fetchAllHealthData() {
    await Promise.allSettled([
        fetchHealthProgramPlan(),
        fetchWaistCircumferenceTrend(),
        fetchWeightTrendData(),
    ])
}

onMounted(() => {
    fetchAllHealthData()
})
</script>

<template>
    <div class="relative">
        <div
            class="w-full h-256px absolute top-0 left-0"
            style="background: linear-gradient(180deg, #CAFFFF 0%, #F4F7F7 100%);"
        ></div>

        <div class="relative pb-40px">
            <div class="flex items-center gap-12px px-16px py-24px">
                <van-image :src="formatHeadImage(userInfo!.headImage, ageAndGender?.gender)" fit="cover" class="of-hidden rounded-10px !w-56px !h-56px flex-shrink-0">
                    <template #loading>
                        <shared-unified-loading size="small" :rainbow="false" />
                    </template>
                </van-image>
                <div ref="debugRef" class="flex justify-between w-full items-center">
                    <div class="flex flex-col items-start space-y-4px">
                        <div class="text-t-5 text-16px font-600">{{ userInfo?.name }}</div>

                        <div v-if="ageAndGender?.age" class="flex items-center gap-4px text-#000000">
                            <span class="text-t-3">{{ ageAndGender.age }}岁</span>
                        </div>
                    </div>

                    <nuxt-link to="/user/settings">
                        <div class="text-14px text-t-5 flex items-center">
                            设置
                            <span class="i-radix-icons:chevron-right ml-4px text-t-4"></span>
                        </div>
                    </nuxt-link>
                </div>
            </div>

            <div class="flex items-center justify-center" @click="showToast('敬请期待')">
                <div class="w-343px h-80px rd-10px relative green-bg">
                    <img src="@/assets/images/user/run.png" class="w-52px h-73px absolute right-10px top-5px" alt="" srcset="" />

                    <div class="mt-16px ml-20px">
                        <div class="flex items-center gap-8px">
                            <div class="i-custom-user-invite w-24px h-24px">
                            </div>
                            <div class="font-fangyuan text-22px text-#1D2229">
                                邀请好友一起减重
                            </div>
                        </div>
                        <div class="text-10px text-t-3">
                            # 好友互相监督，分享饮食/运动心得 #
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex gap-10px justify-center mt-10px">
                <div class="rd-10px w-166px bg-white px-15px py-13px flex flex-col gap-10px" style="box-shadow: 0px 3px 3.2px 0px #00000021;">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-4px">
                            <div class="w-4px h-12px bg-t-2 rd-10px"></div>
                            <div class="text-t-4 text-12px">
                                体重(kg)
                            </div>
                        </div>

                        <div class="flex items-center gap-4px">
                            <div class="text-t-5 text-14px font-600">
                                {{ weightData.hasData ? weightData.value : '-' }}
                            </div>
                            <div v-if="weightData.trend === 'increase'" class="i-custom-increase w-6px h-10px">
                            </div>
                            <div v-else-if="weightData.trend === 'decrease'" class="i-custom-decrease w-6px h-10px">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-4px">
                            <div class="w-4px h-12px bg-t-2 rd-10px"></div>
                            <div class="text-t-4 text-12px">
                                身高(cm)
                            </div>
                        </div>

                        <div class="text-t-5 text-14px font-600">
                            {{ data?.results.archiveHeight || '-' }}
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-4px">
                            <div class="w-4px h-12px bg-t-2 rd-10px"></div>
                            <div class="text-t-4 text-12px">
                                BMI指数
                            </div>
                            <div class="i-custom-question w-11px h-11px" @click="showBMIInfo = true"></div>
                        </div>

                        <div class="flex items-center gap-4px">
                            <div class="text-t-5 text-14px font-600">
                                {{ bmiData.hasData ? bmiData.value : '-' }}
                            </div>
                            <div v-if="bmiData.trend === 'increase'" class="i-custom-increase w-6px h-10px">
                            </div>
                            <div v-else-if="bmiData.trend === 'decrease'" class="i-custom-decrease w-6px h-10px">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rd-10px w-166px bg-white px-15px py-13px flex flex-col gap-10px" style="box-shadow: 0px 3px 3.2px 0px #00000021;">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-4px">
                            <div class="w-4px h-12px bg-t-2 rd-10px"></div>
                            <div class="text-t-4 text-12px">
                                体脂率(%)
                            </div>
                        </div>

                        <div class="flex items-center gap-4px">
                            <div class="text-t-5 text-14px font-600">
                                {{ bodyFatData.hasData ? bodyFatData.value : (data?.results.fatRate || '-') }}
                            </div>
                            <div v-if="bodyFatData.trend === 'increase'" class="i-custom-increase w-6px h-10px">
                            </div>
                            <div v-else-if="bodyFatData.trend === 'decrease'" class="i-custom-decrease w-6px h-10px">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-4px">
                            <div class="w-4px h-12px bg-t-2 rd-10px"></div>
                            <div class="text-t-4 text-12px">
                                腰围(cm)
                            </div>
                        </div>

                        <div class="flex items-center gap-4px">
                            <div class="text-t-5 text-14px font-600">
                                {{ waistCircumferenceData.hasData ? waistCircumferenceData.value : '-' }}
                            </div>
                            <div v-if="waistCircumferenceData.trend === 'increase'" class="i-custom-increase w-6px h-10px">
                            </div>
                            <div v-else-if="waistCircumferenceData.trend === 'decrease'" class="i-custom-decrease w-6px h-10px">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <div class="flex items-center gap-4px">
                            <div class="w-4px h-12px bg-t-2 rd-10px"></div>
                            <div class="text-t-4 text-12px">
                                内脏脂肪面积
                            </div>
                        </div>

                        <div class="flex items-center gap-4px">
                            <div class="text-t-5 text-14px font-600">
                                {{ visceralFatData.hasData ? visceralFatData.value : '-' }}
                            </div>
                            <div v-if="visceralFatData.trend === 'increase'" class="i-custom-increase w-6px h-10px">
                            </div>
                            <div v-else-if="visceralFatData.trend === 'decrease'" class="i-custom-decrease w-6px h-10px">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <van-cell-group inset class="mt-10px! rd-10px!" style="box-shadow: 0px 3px 3.2px 0px #00000021;">
                <van-cell v-for="item in navList.filter(item => item.show)" :key="item.name" is-link class="original" :title="item.name" :icon="item.icon" @click="item.onClick">
                    <template #title>
                        <div class="flex items-center gap-8px">
                            <div :class="item.icon" class="w-16px h-16px"></div>
                            <div class="text-15px text-t-5 relative top-1px">{{ item.name }}</div>
                        </div>
                    </template>

                    <template #value>
                        <div v-if="item.showBadge" class="flex justify-end items-center">
                            <div class="w-16px h-16px bg-primary-6 text-white rd-full flex items-center justify-center text-12px relative top-2px right-3px">
                                {{ (item.num ?? 0) > 99 ? '99+' : (item.num ?? 0) }}
                            </div>
                        </div>
                    </template>
                </van-cell>
            </van-cell-group>
        </div>

        <van-popup
            v-model:show="showBMIInfo"
            round
            closeable
            :style="{ padding: '15px 30px' }"
            class="checkin-center-popup"
        >
            <div class="flex flex-col gap-16px">
                <div class="text-#1D2229 text-18px font-500 w-full text-center">
                    什么是BMI？
                </div>
                <div class="flex flex-col gap-20px">
                    <div class="flex flex-col gap-8px">
                        <div class="text-t-5 font-500">BMI（身体质量指数）是什么？</div>
                        <div class="text-t-4">
                            BMI 是衡量体重是否健康的常用指标，计算方式是：
                            <div class="bg-fill-1 rd-8px p-12px mt-8px text-center font-500">
                                BMI = 体重（kg） ÷ 身高²（m²）
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col gap-8px">
                        <div class="text-t-5 font-500">BMI 区间参考</div>
                        <div class="flex flex-col gap-8px text-t-4">
                            <div class="flex items-center justify-between py-8px px-12px bg-fill-1 rd-8px">
                                <span>偏瘦</span>
                                <span>BMI ＜ 18.5</span>
                            </div>
                            <div class="flex items-center justify-between py-8px px-12px bg-fill-1 rd-8px">
                                <span>正常</span>
                                <span>18.5 ≤ BMI ＜ 24</span>
                            </div>
                            <div class="flex items-center justify-between py-8px px-12px bg-fill-1 rd-8px">
                                <span>超重</span>
                                <span>24 ≤ BMI ＜ 28</span>
                            </div>
                            <div class="flex items-center justify-between py-8px px-12px bg-fill-1 rd-8px">
                                <span>肥胖</span>
                                <span>BMI ≥ 28</span>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-start gap-8px bg-#FFF9F0 p-12px rd-8px">
                        <div class="text-t-4 text-13px">
                            BMI 只是一个基础评估标准，不能完全反映脂肪分布或健康状况。日常还应关注饮食结构、运动习惯和腰围变化。
                        </div>
                    </div>
                </div>

                <van-button
                    type="primary"
                    round
                    class="w-full !h-43px !mt-36px"
                    @click="showBMIInfo = false"
                >
                    知道了
                </van-button>
            </div>
        </van-popup>
    </div>
</template>

<style lang="scss" scoped>
.green-bg {
    background-image: url('@/assets/images/user/green-bg.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    box-shadow: 0px 3px 3.2px 0px #00000021;
}
</style>
