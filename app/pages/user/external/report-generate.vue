<script setup lang="ts">
import type { LocationQuery } from 'vue-router'

useHead({
    title: '生成报告',
})

const skipRequest = ref(false)
const apiFailed = ref(false)
const apiCompleted = ref(false)
const apiFailedStep = ref<'qwen' | 'healthProgram' | null>(null)
const progressRef = ref()
const redirectTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const isComponentMounted = ref(true)

const route = useRoute()
const router = useRouter()

interface QwenResponse {
    empty: boolean
    [key: string]: any
}

const { saveWeightManagementData } = useWeightManagement()

// 处理健康计划指标获取和路由跳转
async function handleHealthProgramRedirect() {
    try {
        const { results: healthResults, state: healthProgramState } = await useWrapFetch<BaseResponse<HealthProgramIndex>>('/api/healthProgram/getHealthProgramIndex')
        if (healthProgramState === 200 && healthResults) {
            const data = { ...healthResults, readStatus: 1 }
            localStorage.setItem('healthResults', JSON.stringify(data))

            if (!isComponentMounted.value) return

            // 保存健康计划数据
            await saveWeightManagementData({
                healthProgramData: data,
                dietMode: healthResults.dietPlan?.dietMode || '地中海饮食模式',
                targetWeight: JSON.parse(healthResults.weightManagementQuestionnaire).height - 105,
                weightCheckIn: JSON.parse(healthResults.weightManagementQuestionnaire).weight,
            })

            redirectTimer.value = setTimeout(() => {
                if (isComponentMounted.value) {
                    router.push({
                        path: '/user/external/weight-management-plan',
                    })
                }
            }, 1000)
            return true
        }
        apiFailedStep.value = skipRequest.value ? 'healthProgram' : 'qwen'
        return false
    } catch (error) {
        console.error('获取或保存健康计划指标失败：', error)
        apiFailedStep.value = skipRequest.value ? 'healthProgram' : 'qwen'
        return false
    }
}

// 处理健康计划重定向并更新状态
async function handleHealthProgramRedirectWithDelay() {
    if (!isComponentMounted.value) return

    const success = await handleHealthProgramRedirect()
    if (success) {
        apiCompleted.value = true
    } else {
        apiFailed.value = true
    }
}

// 调用通义千问接口
async function getQwenResult() {
    const fileIds = extractFileIdsFromRoute(route.query)

    if (!fileIds || fileIds.length === 0) {
        showToast('未获取到报告文件信息')
        return
    }

    apiCompleted.value = false
    console.log('开始生成报告...')
    try {
        const { results } = await useWrapFetch<BaseResponse<QwenResponse>>('/api/qwen/getQwenResult', {
            method: 'post',
            body: {
                fileIds,
            },
        })
        console.log('报告生成完成！', results)
    } catch (error: any) {
        console.error('报告生成失败：', error)
        apiFailedStep.value = 'qwen'
    }

    if (!isComponentMounted.value) return
    const success = await handleHealthProgramRedirect()
    if (success) {
        apiCompleted.value = true
    } else {
        apiFailed.value = true
    }
}

// 重新生成函数
async function handleRegenerate() {
    apiFailed.value = false
    apiCompleted.value = false
    progressRef.value?.restart()

    if (skipRequest.value) {
        handleHealthProgramRedirectWithDelay()
    } else {
        if (apiFailedStep.value === 'qwen') {
            await getQwenResult()
        } else if (apiFailedStep.value === 'healthProgram') {
            const success = await handleHealthProgramRedirect()
            if (success) {
                apiCompleted.value = true
            } else {
                apiFailed.value = true
            }
        }
    }
}

function extractFileIdsFromRoute(routeQuery: LocationQuery): string[] {
    const routeFileIds = routeQuery.fileIds
    if (!routeFileIds) return []
    return (Array.isArray(routeFileIds) ? routeFileIds : [routeFileIds])
        .filter((id): id is string => typeof id === 'string')
}

function handleEnterHome() {
    localStorage.setItem('reportGenerating', '1')
    router.push('/user/checkin')
}

// 处理初始请求 - start
skipRequest.value = Boolean(route.query.skipRequest)

if (skipRequest.value) {
    handleHealthProgramRedirectWithDelay()
} else {
    getQwenResult()
}
// 处理初始请求 - end

const estimatedTimeText = computed(() => {
    const fileIds = extractFileIdsFromRoute(route.query)
    return fileIds && fileIds.length > 0 ? '预计需要3-5分钟' : '预计需要1-2分钟'
})

onBeforeUnmount(() => {
    isComponentMounted.value = false

    if (redirectTimer.value) {
        clearTimeout(redirectTimer.value)
        redirectTimer.value = null
    }
})
</script>

<template>
    <div class="min-h-[100vh] relative px-16px py-24px bg-[url('~/assets/images/external/bg-generate-report.svg')] bg-no-repeat bg-top bg-[length:100%_269px] flex flex-col justify-between items-center bg-#F4F5F7">
        <div class="flex flex-col items-center">
            <div class="text-t-5 text-13px">请耐心等待</div>
            <div class="text-primary-6 text-18px font-600 mb-12px">医生正在设计您的减重方案</div>
            <shared-unified-loading :rainbow="false" />
        </div>
        <div class="flex flex-1 justify-center items-center">
            <img src="@/assets/images/external/doctor.png" class="w-239px h-304px" />
        </div>
        <div class="flex flex-col items-center w-full gap-12px">
            <div class="flex flex-col text-t-4 text-14px text-center gap-6px">
                <div>{{ estimatedTimeText }}</div>
                <div>您可以先进入首页</div>
                <div>稍后会自动推送报告</div>
            </div>
            <van-button v-if="apiFailed" type="primary" round class="!w-240px !h-50px" @click="handleRegenerate">
                重新生成
            </van-button>
            <van-button type="primary" round class="!w-240px !h-50px" @click="handleEnterHome">
                进入小程序首页
            </van-button>
        </div>
    </div>
</template>
