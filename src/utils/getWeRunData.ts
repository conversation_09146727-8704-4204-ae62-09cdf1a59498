import { BASE_URL } from '@/utils/mode'
import Taro from '@tarojs/taro'

export async function getWeRunData() {
  const { code } = await Taro.login()

  try {
    const { encryptedData, iv } = await Taro.getWeRunData()

    const { data } = await Taro.request({
      url: `${BASE_URL}/api/open-api/v1/wx/analyzeWeChatSteps`,
      method: 'GET',
      data: {
        code,
        encryptedData,
        ivStr: iv,
      },
    })

    const results = JSON.parse(data.results)

    const stepInfoList = results.stepInfoList

    const lastDayStepInfo = stepInfoList[stepInfoList.length - 1]

    return {
      code,
      step: lastDayStepInfo.step,
      stepTime: lastDayStepInfo.timestamp,
      lastStepUpdateTime: Math.floor(new Date().getTime() / 1000),
    }
  }
  catch (error) {
    console.error(error)
    return {
      code,
      step: 0,
      stepTime: 0,
      lastStepUpdateTime: 0,
    }
    // throw new Error('获取微信步数失败')
  }
}
