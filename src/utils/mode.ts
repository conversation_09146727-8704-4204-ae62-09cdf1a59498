import Taro from '@tarojs/taro'

export function getMode() {
  // eslint-disable-next-line node/prefer-global/process
  const taroMode = process.env.TARO_APP_MODE
  return Taro.getStorageSync('mode') || taroMode
}

export function getBaseUrl() {
  const PROD_URL = 'https://m.slmc.top'
  // const TEST_URL = 'https://test.slmc.top'
  const TEST_URL = 'http://192.168.110.115:3005'
  const CUSTOM_URL = Taro.getStorageSync('custom-url') || PROD_URL

  switch (getMode()) {
    case 'PROD':
      return PROD_URL
    case 'TEST':
      return TEST_URL
    case 'CUSTOM':
      return CUSTOM_URL
    default:
      return PROD_URL
  }
}

export const BASE_URL = getBaseUrl()
