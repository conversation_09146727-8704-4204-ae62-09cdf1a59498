<script setup lang="ts">
import { getWeRunData } from '@/utils/getWeRunData'
import { BASE_URL } from '@/utils/mode'
import { redirectTo, useDidShow } from '@tarojs/taro'

async function init() {
  try {
    const { step, lastStepUpdateTime, stepTime } = await getWeRunData()

    const url = encodeURIComponent(`${BASE_URL}/user/checkin?step=${step}&lastStepUpdateTime=${lastStepUpdateTime}&stepTime=${stepTime}#sportDialogOpen`)

    redirectTo({
      url: `/pages/webview/index?url=${url}`,
    })
  }
  catch (error) {
    console.log(error)
    const url = encodeURIComponent(`${BASE_URL}/user/checkin?step=0&lastStepUpdateTime=0&stepTime=0`)
    redirectTo({
      url: `/pages/webview/index?url=${url}`,
    })
  }
}

useDidShow(() => {
  init()
})
</script>

<template>
  <img :src="`${BASE_URL}/no-cache/wx-run.png`" alt="" srcset="" mode="widthFix" class="background-image">
</template>

<style>
.background-image {
  object-fit: cover;
  object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
}
</style>
